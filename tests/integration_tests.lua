--[[
    Integration Tests for Luau Advanced Obfuscator
    
    End-to-end tests to validate complete obfuscation pipeline
    and Roblox compatibility.
]]

local TestRunner = require(script.Parent.test_runner)
local Obfuscator = require(script.Parent.Parent.src.main)

-- Create test runner instance
local runner = TestRunner.new()

-- Test: Basic obfuscation functionality
runner:addTest("Basic Obfuscation", function()
    local obfuscator = Obfuscator.new({
        stringObfuscation = true,
        structureObfuscation = true,
        compressionLevel = "medium"
    })
    
    local originalCode = [[
local message = "Hello, World!"
print(message)
]]
    
    local obfuscatedCode = obfuscator:obfuscate(originalCode)
    
    TestRunner.assertType(obfuscatedCode, "string", "Obfuscated code should be a string")
    TestRunner.assertGreaterThan(#obfuscatedCode, 0, "Obfuscated code should not be empty")
    TestRunner.assertNotEqual(obfuscatedCode, originalCode, "Obfuscated code should be different from original")
    
    -- Verify that original strings are not visible
    TestRunner.assertNotContains(obfuscatedCode, "Hello, World!", "Original string should be obfuscated")
    TestRunner.assertNotContains(obfuscatedCode, "message", "Variable names should be obfuscated")
end, "Tests basic obfuscation with string and structure obfuscation enabled")

-- Test: String obfuscation effectiveness
runner:addTest("String Obfuscation", function()
    local obfuscator = Obfuscator.new({
        stringObfuscation = true,
        structureObfuscation = false,
        antiAnalysis = false,
        environmentManipulation = false
    })
    
    local originalCode = [[
local str1 = "Test String 1"
local str2 = "Another test string"
local str3 = "Special chars: !@#$%^&*()"
]]
    
    local obfuscatedCode = obfuscator:obfuscate(originalCode)
    
    -- Original strings should not be visible
    TestRunner.assertNotContains(obfuscatedCode, "Test String 1", "First string should be obfuscated")
    TestRunner.assertNotContains(obfuscatedCode, "Another test string", "Second string should be obfuscated")
    TestRunner.assertNotContains(obfuscatedCode, "Special chars: !@#$%^&*()", "Third string should be obfuscated")
    
    -- Should contain obfuscation artifacts
    TestRunner.assertContains(obfuscatedCode, "local", "Should still contain Lua keywords")
    
    local stats = obfuscator:getStats()
    TestRunner.assertGreaterThan(stats.stringObfuscations.stringsObfuscated, 0, "Should have obfuscated some strings")
end, "Tests string obfuscation module effectiveness")

-- Test: Variable name obfuscation
runner:addTest("Variable Name Obfuscation", function()
    local obfuscator = Obfuscator.new({
        stringObfuscation = false,
        structureObfuscation = true,
        antiAnalysis = false,
        environmentManipulation = false
    })
    
    local originalCode = [[
local myVariable = 42
local anotherVar = "test"
function myFunction(param1, param2)
    return param1 + param2
end
]]
    
    local obfuscatedCode = obfuscator:obfuscate(originalCode)
    
    -- Original variable names should not be visible
    TestRunner.assertNotContains(obfuscatedCode, "myVariable", "Variable name should be obfuscated")
    TestRunner.assertNotContains(obfuscatedCode, "anotherVar", "Variable name should be obfuscated")
    TestRunner.assertNotContains(obfuscatedCode, "myFunction", "Function name should be obfuscated")
    TestRunner.assertNotContains(obfuscatedCode, "param1", "Parameter name should be obfuscated")
    TestRunner.assertNotContains(obfuscatedCode, "param2", "Parameter name should be obfuscated")
    
    local stats = obfuscator:getStats()
    TestRunner.assertGreaterThan(stats.structureObfuscations.variablesRenamed, 0, "Should have renamed some variables")
end, "Tests variable and function name obfuscation")

-- Test: Anti-analysis features
runner:addTest("Anti-Analysis Features", function()
    local obfuscator = Obfuscator.new({
        stringObfuscation = false,
        structureObfuscation = false,
        antiAnalysis = true,
        enableVMDetection = true,
        enableDynamicExecution = true
    })
    
    local originalCode = [[
local value = 123
print(value)
]]
    
    local obfuscatedCode = obfuscator:obfuscate(originalCode)
    
    -- Should contain anti-analysis artifacts
    TestRunner.assertContains(obfuscatedCode, "pcall", "Should contain VM detection code")
    TestRunner.assertContains(obfuscatedCode, "type", "Should contain type checking")
    
    local stats = obfuscator:getStats()
    TestRunner.assertGreaterThan(stats.antiAnalysisFeatures.vmDetectionChecks, 0, "Should have VM detection checks")
end, "Tests anti-analysis and evasion techniques")

-- Test: Environment manipulation
runner:addTest("Environment Manipulation", function()
    local obfuscator = Obfuscator.new({
        stringObfuscation = false,
        structureObfuscation = false,
        antiAnalysis = false,
        environmentManipulation = true
    })
    
    local originalCode = [[
local test = "environment test"
]]
    
    local obfuscatedCode = obfuscator:obfuscate(originalCode)
    
    -- Should contain environment manipulation
    TestRunner.assertContains(obfuscatedCode, "_ENV", "Should manipulate _ENV")
    TestRunner.assertContains(obfuscatedCode, "getfenv", "Should use getfenv")
    TestRunner.assertContains(obfuscatedCode, "setfenv", "Should use setfenv")
    
    local stats = obfuscator:getStats()
    TestRunner.assertGreaterThan(stats.environmentManipulations.environmentHijacks, 0, "Should have environment hijacks")
end, "Tests environment manipulation techniques")

-- Test: High compression obfuscation
runner:addTest("High Compression", function()
    local obfuscator = Obfuscator.new({
        stringObfuscation = true,
        structureObfuscation = true,
        antiAnalysis = true,
        environmentManipulation = true,
        compressionLevel = "high"
    })
    
    local originalCode = TestRunner.generateTestLuaCode()
    local obfuscatedCode = obfuscator:obfuscate(originalCode)
    
    -- Should be compressed (fewer newlines)
    local originalLines = 0
    local obfuscatedLines = 0
    
    for _ in originalCode:gmatch("\n") do
        originalLines = originalLines + 1
    end
    
    for _ in obfuscatedCode:gmatch("\n") do
        obfuscatedLines = obfuscatedLines + 1
    end
    
    TestRunner.assertLessThan(obfuscatedLines, originalLines, "Compressed code should have fewer lines")
    
    -- Should still be functional Lua code
    TestRunner.assertContains(obfuscatedCode, "local", "Should contain Lua syntax")
    TestRunner.assertContains(obfuscatedCode, "function", "Should contain function declarations")
end, "Tests high compression obfuscation mode")

-- Test: Complex code obfuscation
runner:addTest("Complex Code Obfuscation", function()
    local obfuscator = Obfuscator.new({
        stringObfuscation = true,
        structureObfuscation = true,
        antiAnalysis = true,
        environmentManipulation = true,
        compressionLevel = "high"
    })
    
    local originalCode = TestRunner.generateComplexLuaCode()
    local obfuscatedCode = obfuscator:obfuscate(originalCode)
    
    TestRunner.assertType(obfuscatedCode, "string", "Should return string")
    TestRunner.assertGreaterThan(#obfuscatedCode, 0, "Should not be empty")
    TestRunner.assertNotEqual(obfuscatedCode, originalCode, "Should be different from original")
    
    -- Original identifiers should not be visible
    TestRunner.assertNotContains(obfuscatedCode, "complexFunction", "Function name should be obfuscated")
    TestRunner.assertNotContains(obfuscatedCode, "metatable", "Variable name should be obfuscated")
    TestRunner.assertNotContains(obfuscatedCode, "dynamicExecution", "Function name should be obfuscated")
end, "Tests obfuscation of complex Lua code with advanced features")

-- Test: Obfuscation statistics
runner:addTest("Obfuscation Statistics", function()
    local obfuscator = Obfuscator.new({
        stringObfuscation = true,
        structureObfuscation = true,
        antiAnalysis = true,
        environmentManipulation = true
    })
    
    local originalCode = TestRunner.generateTestLuaCode()
    obfuscator:obfuscate(originalCode)
    
    local stats = obfuscator:getStats()
    
    TestRunner.assertType(stats, "table", "Stats should be a table")
    TestRunner.assertType(stats.stringObfuscations, "table", "String stats should be a table")
    TestRunner.assertType(stats.structureObfuscations, "table", "Structure stats should be a table")
    TestRunner.assertType(stats.antiAnalysisFeatures, "table", "Anti-analysis stats should be a table")
    TestRunner.assertType(stats.environmentManipulations, "table", "Environment stats should be a table")
    
    -- Should have some obfuscation activity
    TestRunner.assertGreaterThan(stats.stringObfuscations.stringsObfuscated, 0, "Should obfuscate strings")
    TestRunner.assertGreaterThan(stats.structureObfuscations.variablesRenamed, 0, "Should rename variables")
end, "Tests obfuscation statistics collection and reporting")

-- Test: Configuration validation
runner:addTest("Configuration Validation", function()
    -- Test with minimal configuration
    local obfuscator1 = Obfuscator.new({})
    TestRunner.assertType(obfuscator1, "table", "Should create obfuscator with empty config")
    
    -- Test with custom configuration
    local obfuscator2 = Obfuscator.new({
        stringObfuscation = false,
        structureObfuscation = false,
        antiAnalysis = false,
        environmentManipulation = false,
        compressionLevel = "low"
    })
    TestRunner.assertType(obfuscator2, "table", "Should create obfuscator with custom config")
    
    local originalCode = "local test = 123"
    local result1 = obfuscator1:obfuscate(originalCode)
    local result2 = obfuscator2:obfuscate(originalCode)
    
    TestRunner.assertType(result1, "string", "Should return string with default config")
    TestRunner.assertType(result2, "string", "Should return string with custom config")
end, "Tests configuration validation and default values")

-- Test: Error handling
runner:addTest("Error Handling", function()
    local obfuscator = Obfuscator.new()
    
    -- Test with invalid input
    local success1, result1 = pcall(function()
        return obfuscator:obfuscate(nil)
    end)
    TestRunner.assert(not success1, "Should fail with nil input")
    
    local success2, result2 = pcall(function()
        return obfuscator:obfuscate(123)
    end)
    TestRunner.assert(not success2, "Should fail with number input")
    
    -- Test with empty string
    local result3 = obfuscator:obfuscate("")
    TestRunner.assertEqual(result3, "", "Should handle empty string")
    
    -- Test with whitespace only
    local result4 = obfuscator:obfuscate("   \n\t  ")
    TestRunner.assertType(result4, "string", "Should handle whitespace-only input")
end, "Tests error handling for invalid inputs")

-- Run all tests
print("Starting Luau Advanced Obfuscator Integration Tests...")
local success = runner:runAll()

if success then
    print("\n🎉 All tests passed! The obfuscator is working correctly.")
else
    print("\n❌ Some tests failed. Please review the failures above.")
end

return success
