--[[
    Test Runner for Luau Advanced Obfuscator
    
    Comprehensive testing framework to validate obfuscation effectiveness
    and Roblox compatibility.
]]

local TestRunner = {}
TestRunner.__index = TestRunner

-- Test result types
local TestResult = {
    PASS = "PASS",
    FAIL = "FAIL",
    SKIP = "SKIP"
}

-- Create new test runner instance
function TestRunner.new()
    local self = setmetatable({}, TestRunner)
    
    self.tests = {}
    self.results = {}
    self.stats = {
        total = 0,
        passed = 0,
        failed = 0,
        skipped = 0
    }
    
    return self
end

-- Add test to runner
function TestRunner:addTest(name, testFunction, description)
    table.insert(self.tests, {
        name = name,
        func = testFunction,
        description = description or ""
    })
end

-- Run all tests
function TestRunner:runAll()
    print("=== Luau Advanced Obfuscator Test Suite ===")
    print("Running " .. #self.tests .. " tests...\n")
    
    for i, test in ipairs(self.tests) do
        self:runTest(test, i)
    end
    
    self:printSummary()
    return self.stats.failed == 0
end

-- Run single test
function TestRunner:runTest(test, index)
    local startTime = tick()
    local success, result = pcall(test.func)
    local endTime = tick()
    local duration = endTime - startTime
    
    local testResult = {
        name = test.name,
        description = test.description,
        success = success,
        result = result,
        duration = duration,
        status = success and TestResult.PASS or TestResult.FAIL
    }
    
    table.insert(self.results, testResult)
    self.stats.total = self.stats.total + 1
    
    if success then
        self.stats.passed = self.stats.passed + 1
        print(string.format("[%d/%d] ✓ %s (%.3fs)", index, #self.tests, test.name, duration))
    else
        self.stats.failed = self.stats.failed + 1
        print(string.format("[%d/%d] ✗ %s (%.3fs)", index, #self.tests, test.name, duration))
        print("    Error: " .. tostring(result))
    end
    
    if test.description and #test.description > 0 then
        print("    " .. test.description)
    end
    
    print()
end

-- Print test summary
function TestRunner:printSummary()
    print("=== Test Summary ===")
    print(string.format("Total: %d", self.stats.total))
    print(string.format("Passed: %d", self.stats.passed))
    print(string.format("Failed: %d", self.stats.failed))
    print(string.format("Skipped: %d", self.stats.skipped))
    
    local passRate = self.stats.total > 0 and (self.stats.passed / self.stats.total * 100) or 0
    print(string.format("Pass Rate: %.1f%%", passRate))
    
    if self.stats.failed > 0 then
        print("\n=== Failed Tests ===")
        for _, result in ipairs(self.results) do
            if result.status == TestResult.FAIL then
                print("✗ " .. result.name)
                print("  " .. tostring(result.result))
            end
        end
    end
end

-- Assert functions for tests
function TestRunner.assert(condition, message)
    if not condition then
        error(message or "Assertion failed")
    end
end

function TestRunner.assertEqual(actual, expected, message)
    if actual ~= expected then
        error(string.format("%s\nExpected: %s\nActual: %s", 
            message or "Values not equal", tostring(expected), tostring(actual)))
    end
end

function TestRunner.assertNotEqual(actual, expected, message)
    if actual == expected then
        error(string.format("%s\nExpected values to be different, but both were: %s", 
            message or "Values should not be equal", tostring(actual)))
    end
end

function TestRunner.assertType(value, expectedType, message)
    local actualType = type(value)
    if actualType ~= expectedType then
        error(string.format("%s\nExpected type: %s\nActual type: %s", 
            message or "Type mismatch", expectedType, actualType))
    end
end

function TestRunner.assertContains(haystack, needle, message)
    if type(haystack) == "string" then
        if not haystack:find(needle, 1, true) then
            error(string.format("%s\nString '%s' does not contain '%s'", 
                message or "String does not contain expected substring", haystack, needle))
        end
    elseif type(haystack) == "table" then
        local found = false
        for _, value in pairs(haystack) do
            if value == needle then
                found = true
                break
            end
        end
        if not found then
            error(string.format("%s\nTable does not contain value: %s", 
                message or "Table does not contain expected value", tostring(needle)))
        end
    else
        error("assertContains only works with strings and tables")
    end
end

function TestRunner.assertNotContains(haystack, needle, message)
    if type(haystack) == "string" then
        if haystack:find(needle, 1, true) then
            error(string.format("%s\nString '%s' should not contain '%s'", 
                message or "String contains unexpected substring", haystack, needle))
        end
    elseif type(haystack) == "table" then
        for _, value in pairs(haystack) do
            if value == needle then
                error(string.format("%s\nTable should not contain value: %s", 
                    message or "Table contains unexpected value", tostring(needle)))
            end
        end
    else
        error("assertNotContains only works with strings and tables")
    end
end

function TestRunner.assertGreaterThan(actual, expected, message)
    if actual <= expected then
        error(string.format("%s\nExpected %s to be greater than %s", 
            message or "Value not greater than expected", tostring(actual), tostring(expected)))
    end
end

function TestRunner.assertLessThan(actual, expected, message)
    if actual >= expected then
        error(string.format("%s\nExpected %s to be less than %s", 
            message or "Value not less than expected", tostring(actual), tostring(expected)))
    end
end

-- Utility functions for testing
function TestRunner.measureExecutionTime(func)
    local startTime = tick()
    local result = func()
    local endTime = tick()
    return result, endTime - startTime
end

function TestRunner.createMockFunction()
    local calls = {}
    local returnValue = nil
    
    local mock = function(...)
        table.insert(calls, {...})
        return returnValue
    end
    
    mock.getCalls = function()
        return calls
    end
    
    mock.getCallCount = function()
        return #calls
    end
    
    mock.setReturnValue = function(value)
        returnValue = value
    end
    
    mock.reset = function()
        calls = {}
        returnValue = nil
    end
    
    return mock
end

-- Test data generators
function TestRunner.generateRandomString(length)
    local chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    local result = ""
    
    for i = 1, length do
        local randomIndex = math.random(1, #chars)
        result = result .. chars:sub(randomIndex, randomIndex)
    end
    
    return result
end

function TestRunner.generateTestLuaCode()
    return [[
local function greet(name)
    local message = "Hello, " .. name .. "!"
    print(message)
    return message
end

local function calculate(a, b)
    if a > b then
        return a + b
    else
        return a * b
    end
end

local result = calculate(5, 3)
greet("World")

for i = 1, 3 do
    print("Iteration: " .. i)
end

local data = {
    name = "Test",
    value = 42,
    active = true
}

return result
]]
end

function TestRunner.generateComplexLuaCode()
    return [[
local _ENV = _ENV
local getfenv = getfenv
local setfenv = setfenv
local loadstring = loadstring

local function complexFunction(...)
    local args = {...}
    local result = {}
    
    for i, arg in ipairs(args) do
        if type(arg) == "string" then
            result[i] = arg:upper()
        elseif type(arg) == "number" then
            result[i] = arg * 2
        else
            result[i] = tostring(arg)
        end
    end
    
    return unpack(result)
end

local metatable = {
    __index = function(t, k)
        return "default_" .. k
    end,
    __newindex = function(t, k, v)
        rawset(t, "_" .. k, v)
    end
}

local proxy = setmetatable({}, metatable)
proxy.test = "value"

local function dynamicExecution(code)
    local func = loadstring(code)
    if func then
        local env = getfenv(func)
        setfenv(func, env)
        return func()
    end
end

return {
    complex = complexFunction,
    proxy = proxy,
    execute = dynamicExecution
}
]]
end

return TestRunner
