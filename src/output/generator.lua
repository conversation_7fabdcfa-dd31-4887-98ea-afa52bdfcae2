--[[
    Code Generator for Obfuscated Output
    
    Generates obfuscated Lua code from the processed AST.
    Handles all obfuscation transformations and produces final output.
]]

local Generator = {}
Generator.__index = Generator

local Utils = require(script.Parent.Parent.modules.utils)

-- Create new generator instance
function Generator.new(config)
    local self = setmetatable({}, Generator)
    self.config = config
    self.indentLevel = 0
    self.output = {}
    return self
end

-- Generate code from AST
function Generator:generate(ast)
    if not ast then
        return ""
    end
    
    self.output = {}
    self.indentLevel = 0
    
    self:generateNode(ast)
    
    local result = table.concat(self.output)
    
    -- Apply final formatting based on configuration
    if self.config.compressionLevel == "high" then
        result = self:compressCode(result)
    end
    
    return result
end

-- Generate code for AST node
function Generator:generateNode(node)
    if not node or type(node) ~= "table" then
        return
    end
    
    local nodeType = node.type
    
    if nodeType == "program" then
        self:generateProgram(node)
    elseif nodeType == "block" then
        self:generateBlock(node)
    elseif nodeType == "local_declaration" then
        self:generateLocalDeclaration(node)
    elseif nodeType == "assignment" then
        self:generateAssignment(node)
    elseif nodeType == "function_declaration" then
        self:generateFunctionDeclaration(node)
    elseif nodeType == "if_statement" then
        self:generateIfStatement(node)
    elseif nodeType == "while_loop" then
        self:generateWhileLoop(node)
    elseif nodeType == "for_loop" then
        self:generateForLoop(node)
    elseif nodeType == "return_statement" then
        self:generateReturnStatement(node)
    elseif nodeType == "break_statement" then
        self:generateBreakStatement(node)
    elseif nodeType == "expression_statement" then
        self:generateExpressionStatement(node)
    elseif nodeType == "identifier" then
        self:generateIdentifier(node)
    elseif nodeType == "literal" then
        self:generateLiteral(node)
    elseif nodeType == "binary_operation" then
        self:generateBinaryOperation(node)
    elseif nodeType == "unary_operation" then
        self:generateUnaryOperation(node)
    elseif nodeType == "function_call" then
        self:generateFunctionCall(node)
    elseif nodeType == "function" then
        self:generateFunction(node)
    elseif nodeType == "table_constructor" then
        self:generateTableConstructor(node)
    elseif nodeType == "dynamic_string" then
        self:generateDynamicString(node)
    end
end

-- Generate program
function Generator:generateProgram(node)
    if node.body then
        for i, statement in ipairs(node.body) do
            self:generateNode(statement)
            if i < #node.body then
                self:addNewline()
            end
        end
    end
end

-- Generate block
function Generator:generateBlock(node)
    if node.statements then
        for i, statement in ipairs(node.statements) do
            self:generateNode(statement)
            if i < #node.statements then
                self:addNewline()
            end
        end
    end
end

-- Generate local declaration
function Generator:generateLocalDeclaration(node)
    self:addIndent()
    self:add("local ")
    
    if node.name then
        self:add(node.name)
    end
    
    if node.value then
        self:add(" = ")
        self:generateNode(node.value)
    end
end

-- Generate assignment
function Generator:generateAssignment(node)
    self:addIndent()
    
    if node.target then
        self:generateNode(node.target)
    end
    
    self:add(" = ")
    
    if node.value then
        self:generateNode(node.value)
    end
end

-- Generate function declaration
function Generator:generateFunctionDeclaration(node)
    self:addIndent()
    self:add("function")
    
    if node.name then
        self:add(" " .. node.name)
    end
    
    self:add("(")
    
    if node.parameters then
        for i, param in ipairs(node.parameters) do
            self:add(param)
            if i < #node.parameters then
                self:add(", ")
            end
        end
    end
    
    self:add(")")
    self:addNewline()
    
    self:increaseIndent()
    if node.body then
        self:generateNode(node.body)
    end
    self:decreaseIndent()
    
    self:addNewline()
    self:addIndent()
    self:add("end")
end

-- Generate if statement
function Generator:generateIfStatement(node)
    self:addIndent()
    self:add("if ")
    
    if node.condition then
        self:generateNode(node.condition)
    end
    
    self:add(" then")
    self:addNewline()
    
    self:increaseIndent()
    if node.then_block then
        self:generateNode(node.then_block)
    end
    self:decreaseIndent()
    
    if node.else_block then
        self:addNewline()
        self:addIndent()
        self:add("else")
        self:addNewline()
        
        self:increaseIndent()
        self:generateNode(node.else_block)
        self:decreaseIndent()
    end
    
    self:addNewline()
    self:addIndent()
    self:add("end")
end

-- Generate while loop
function Generator:generateWhileLoop(node)
    self:addIndent()
    self:add("while ")
    
    if node.condition then
        self:generateNode(node.condition)
    end
    
    self:add(" do")
    self:addNewline()
    
    self:increaseIndent()
    if node.body then
        self:generateNode(node.body)
    end
    self:decreaseIndent()
    
    self:addNewline()
    self:addIndent()
    self:add("end")
end

-- Generate for loop
function Generator:generateForLoop(node)
    self:addIndent()
    self:add("for ")
    
    if node.variable then
        self:add(node.variable .. " = ")
    end
    
    if node.start then
        self:generateNode(node.start)
    end
    
    self:add(", ")
    
    if node.stop then
        self:generateNode(node.stop)
    end
    
    if node.step then
        self:add(", ")
        self:generateNode(node.step)
    end
    
    self:add(" do")
    self:addNewline()
    
    self:increaseIndent()
    if node.body then
        self:generateNode(node.body)
    end
    self:decreaseIndent()
    
    self:addNewline()
    self:addIndent()
    self:add("end")
end

-- Generate return statement
function Generator:generateReturnStatement(node)
    self:addIndent()
    self:add("return")
    
    if node.value then
        self:add(" ")
        self:generateNode(node.value)
    end
end

-- Generate break statement
function Generator:generateBreakStatement(node)
    self:addIndent()
    self:add("break")
end

-- Generate expression statement
function Generator:generateExpressionStatement(node)
    self:addIndent()
    if node.expression then
        self:generateNode(node.expression)
    end
end

-- Generate identifier
function Generator:generateIdentifier(node)
    if node.name then
        self:add(node.name)
    end
end

-- Generate literal
function Generator:generateLiteral(node)
    if node.value ~= nil then
        if type(node.value) == "string" then
            self:add('"' .. Utils.escapeString(node.value) .. '"')
        else
            self:add(tostring(node.value))
        end
    end
end

-- Generate binary operation
function Generator:generateBinaryOperation(node)
    if node.left then
        self:generateNode(node.left)
    end
    
    if node.operator then
        self:add(" " .. node.operator .. " ")
    end
    
    if node.right then
        self:generateNode(node.right)
    end
end

-- Generate unary operation
function Generator:generateUnaryOperation(node)
    if node.operator then
        self:add(node.operator)
    end
    
    if node.operand then
        self:generateNode(node.operand)
    end
end

-- Generate function call
function Generator:generateFunctionCall(node)
    if node.name then
        self:add(node.name)
    end
    
    self:add("(")
    
    if node.arguments then
        for i, arg in ipairs(node.arguments) do
            self:generateNode(arg)
            if i < #node.arguments then
                self:add(", ")
            end
        end
    end
    
    self:add(")")
end

-- Generate function
function Generator:generateFunction(node)
    self:add("function(")
    
    if node.parameters then
        for i, param in ipairs(node.parameters) do
            self:add(param)
            if i < #node.parameters then
                self:add(", ")
            end
        end
    end
    
    self:add(")")
    self:addNewline()
    
    self:increaseIndent()
    if node.body then
        self:generateNode(node.body)
    end
    self:decreaseIndent()
    
    self:addNewline()
    self:addIndent()
    self:add("end")
end

-- Generate table constructor
function Generator:generateTableConstructor(node)
    self:add("{")
    
    if node.fields then
        for i, field in ipairs(node.fields) do
            if field.key then
                self:add("[")
                self:generateNode(field.key)
                self:add("] = ")
            end
            
            if field.value then
                self:generateNode(field.value)
            end
            
            if i < #node.fields then
                self:add(", ")
            end
        end
    end
    
    self:add("}")
end

-- Generate dynamic string (obfuscated string)
function Generator:generateDynamicString(node)
    if node.construction and node.construction.code then
        self:add("(function()")
        self:addNewline()
        self:increaseIndent()
        
        -- Add the construction code
        self:addIndent()
        self:add(node.construction.code)
        self:addNewline()
        
        self:addIndent()
        self:add("return " .. node.construction.variable)
        self:addNewline()
        
        self:decreaseIndent()
        self:addIndent()
        self:add("end)()")
    else
        -- Fallback to original value
        self:add('"' .. Utils.escapeString(node.original_value or "") .. '"')
    end
end

-- Helper methods
function Generator:add(text)
    table.insert(self.output, text)
end

function Generator:addNewline()
    if self.config.preserveWhitespace ~= false then
        table.insert(self.output, "\n")
    end
end

function Generator:addIndent()
    if self.config.preserveWhitespace ~= false then
        for i = 1, self.indentLevel do
            table.insert(self.output, "    ")
        end
    end
end

function Generator:increaseIndent()
    self.indentLevel = self.indentLevel + 1
end

function Generator:decreaseIndent()
    self.indentLevel = math.max(0, self.indentLevel - 1)
end

-- Compress code to single line
function Generator:compressCode(code)
    -- Remove all whitespace and newlines while preserving string literals
    local compressed = ""
    local inString = false
    local stringChar = nil
    local escaped = false
    
    for i = 1, #code do
        local char = code:sub(i, i)
        
        if escaped then
            compressed = compressed .. char
            escaped = false
        elseif char == "\\" and inString then
            compressed = compressed .. char
            escaped = true
        elseif (char == '"' or char == "'") and not inString then
            inString = true
            stringChar = char
            compressed = compressed .. char
        elseif char == stringChar and inString then
            inString = false
            stringChar = nil
            compressed = compressed .. char
        elseif inString then
            compressed = compressed .. char
        elseif char:match("%s") then
            -- Skip whitespace outside strings, but preserve single spaces between tokens
            if compressed:sub(-1) ~= " " and not compressed:sub(-1):match("[(){}%[%],;=]") then
                compressed = compressed .. " "
            end
        else
            compressed = compressed .. char
        end
    end
    
    -- Clean up extra spaces
    compressed = compressed:gsub("  +", " ")
    compressed = compressed:gsub(" ([(){}%[%],;=])", "%1")
    compressed = compressed:gsub("([(){}%[%],;=]) ", "%1")
    
    return compressed
end

return Generator
