--[[
    Environment Manipulation Module
    
    Implements global environment hijacking, function redefinition,
    and execution context manipulation.
    
    Based on Project Madara techniques:
    - Global environment hijacking
    - Function redefinition
    - Execution context manipulation
]]

local Environment = {}
Environment.__index = Environment

local Utils = require(script.Parent.utils)

-- Create new environment manipulation instance
function Environment.new(config)
    local self = setmetatable({}, Environment)
    
    self.config = config
    self.stats = {
        environmentHijacks = 0,
        functionRedefinitions = 0,
        contextManipulations = 0
    }
    
    return self
end

-- Apply environment manipulation to AST
function Environment:applyEnvironmentManipulation(ast)
    if not ast or type(ast) ~= "table" then
        return ast
    end
    
    -- Add global environment hijacking
    ast = self:addEnvironmentHijacking(ast)
    
    -- Add function redefinition
    ast = self:addFunctionRedefinition(ast)
    
    -- Add execution context manipulation
    ast = self:addContextManipulation(ast)
    
    return ast
end

-- Add global environment hijacking (Project Madara: _ENV manipulation)
function Environment:addEnvironmentHijacking(ast)
    local envVar = Utils.generateRandomVariableName() -- Like twuTsJpSydrWUjTa in Project Madara
    local backupVar = Utils.generateRandomVariableName()
    
    local hijackCode = {
        type = "block",
        statements = {
            {
                type = "local_declaration",
                name = backupVar,
                value = {type = "identifier", name = "_ENV"}
            },
            {
                type = "local_declaration",
                name = envVar,
                value = {type = "identifier", name = "_ENV"}
            },
            {
                type = "assignment",
                target = {type = "identifier", name = "_ENV"},
                value = {
                    type = "function_call",
                    name = "setmetatable",
                    arguments = {
                        {type = "table_constructor", fields = {}},
                        {
                            type = "table_constructor",
                            fields = {
                                {
                                    key = {type = "literal", value = "__index"},
                                    value = {type = "identifier", name = envVar}
                                },
                                {
                                    key = {type = "literal", value = "__newindex"},
                                    value = {type = "identifier", name = envVar}
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    -- Insert at the beginning of the program
    if ast.type == "program" and ast.body then
        table.insert(ast.body, 1, hijackCode)
    elseif ast.type == "block" and ast.statements then
        table.insert(ast.statements, 1, hijackCode)
    end
    
    self.stats.environmentHijacks = self.stats.environmentHijacks + 1
    return ast
end

-- Add function redefinition (Project Madara: getfenv, loadstring redefinition)
function Environment:addFunctionRedefinition(ast)
    local redefinitions = self:generateFunctionRedefinitions()
    
    -- Insert redefinitions at the beginning
    if ast.type == "program" and ast.body then
        for i = #redefinitions, 1, -1 do
            table.insert(ast.body, 1, redefinitions[i])
        end
    elseif ast.type == "block" and ast.statements then
        for i = #redefinitions, 1, -1 do
            table.insert(ast.statements, 1, redefinitions[i])
        end
    end
    
    self.stats.functionRedefinitions = self.stats.functionRedefinitions + #redefinitions
    return ast
end

-- Generate function redefinitions
function Environment:generateFunctionRedefinitions()
    local redefinitions = {}
    
    -- Redefine loadstring
    local loadstringBackup = Utils.generateRandomVariableName()
    local loadstringRedefinition = {
        type = "block",
        statements = {
            {
                type = "local_declaration",
                name = loadstringBackup,
                value = {type = "identifier", name = "loadstring"}
            },
            {
                type = "assignment",
                target = {type = "identifier", name = "loadstring"},
                value = {
                    type = "function",
                    parameters = {"code", "chunkname"},
                    body = {
                        type = "block",
                        statements = {
                            {
                                type = "if_statement",
                                condition = {
                                    type = "binary_operation",
                                    operator = "and",
                                    left = {
                                        type = "identifier",
                                        name = "code"
                                    },
                                    right = {
                                        type = "function_call",
                                        name = "type",
                                        arguments = {{type = "identifier", name = "code"}}
                                    }
                                },
                                then_block = {
                                    type = "block",
                                    statements = {
                                        {
                                            type = "return_statement",
                                            value = {
                                                type = "function_call",
                                                name = loadstringBackup,
                                                arguments = {
                                                    {type = "identifier", name = "code"},
                                                    {type = "identifier", name = "chunkname"}
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    table.insert(redefinitions, loadstringRedefinition)
    
    -- Redefine getfenv
    local getfenvBackup = Utils.generateRandomVariableName()
    local getfenvRedefinition = {
        type = "block",
        statements = {
            {
                type = "local_declaration",
                name = getfenvBackup,
                value = {type = "identifier", name = "getfenv"}
            },
            {
                type = "assignment",
                target = {type = "identifier", name = "getfenv"},
                value = {
                    type = "function",
                    parameters = {"level"},
                    body = {
                        type = "block",
                        statements = {
                            {
                                type = "return_statement",
                                value = {
                                    type = "function_call",
                                    name = getfenvBackup,
                                    arguments = {
                                        {
                                            type = "binary_operation",
                                            operator = "or",
                                            left = {type = "identifier", name = "level"},
                                            right = {type = "literal", value = 1}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    table.insert(redefinitions, getfenvRedefinition)
    
    -- Redefine setfenv
    local setfenvBackup = Utils.generateRandomVariableName()
    local setfenvRedefinition = {
        type = "block",
        statements = {
            {
                type = "local_declaration",
                name = setfenvBackup,
                value = {type = "identifier", name = "setfenv"}
            },
            {
                type = "assignment",
                target = {type = "identifier", name = "setfenv"},
                value = {
                    type = "function",
                    parameters = {"func", "env"},
                    body = {
                        type = "block",
                        statements = {
                            {
                                type = "return_statement",
                                value = {
                                    type = "function_call",
                                    name = setfenvBackup,
                                    arguments = {
                                        {type = "identifier", name = "func"},
                                        {type = "identifier", name = "env"}
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    table.insert(redefinitions, setfenvRedefinition)
    
    return redefinitions
end

-- Add execution context manipulation
function Environment:addContextManipulation(ast)
    local contextCode = self:generateContextManipulation()
    
    -- Insert context manipulation throughout the code
    if ast.type == "block" and ast.statements then
        local insertPositions = {}
        for i = 1, math.min(2, math.floor(#ast.statements / 3)) do
            table.insert(insertPositions, math.random(1, #ast.statements + 1))
        end
        
        table.sort(insertPositions, function(a, b) return a > b end)
        
        for _, pos in ipairs(insertPositions) do
            table.insert(ast.statements, pos, contextCode)
        end
    end
    
    self.stats.contextManipulations = self.stats.contextManipulations + 1
    return ast
end

-- Generate execution context manipulation code
function Environment:generateContextManipulation()
    local contextVar = Utils.generateRandomVariableName()
    local envVar = Utils.generateRandomVariableName()
    local funcVar = Utils.generateRandomVariableName()
    
    return {
        type = "block",
        statements = {
            {
                type = "local_declaration",
                name = contextVar,
                value = {
                    type = "function_call",
                    name = "getfenv",
                    arguments = {{type = "literal", value = 1}}
                }
            },
            {
                type = "local_declaration",
                name = envVar,
                value = {
                    type = "function_call",
                    name = "setmetatable",
                    arguments = {
                        {type = "table_constructor", fields = {}},
                        {
                            type = "table_constructor",
                            fields = {
                                {
                                    key = {type = "literal", value = "__index"},
                                    value = {type = "identifier", name = contextVar}
                                }
                            }
                        }
                    }
                }
            },
            {
                type = "local_declaration",
                name = funcVar,
                value = {
                    type = "function",
                    parameters = {},
                    body = {
                        type = "block",
                        statements = {
                            {
                                type = "return_statement",
                                value = {type = "identifier", name = envVar}
                            }
                        }
                    }
                }
            },
            {
                type = "expression_statement",
                expression = {
                    type = "function_call",
                    name = "setfenv",
                    arguments = {
                        {type = "identifier", name = funcVar},
                        {type = "identifier", name = envVar}
                    }
                }
            }
        }
    }
end

-- Create isolated execution environment
function Environment:createIsolatedEnvironment()
    local isolatedVar = Utils.generateRandomVariableName()
    local sandboxVar = Utils.generateRandomVariableName()
    local executorVar = Utils.generateRandomVariableName()
    
    return string.format([[
local %s = {}
local %s = setmetatable(%s, {
    __index = function(t, k)
        if k == "_G" then return %s end
        if k == "_ENV" then return %s end
        return _G[k]
    end,
    __newindex = function(t, k, v)
        %s[k] = v
    end
})

local %s = function(code)
    local func = loadstring(code)
    if func then
        setfenv(func, %s)
        return func()
    end
end
]], 
        isolatedVar, sandboxVar, isolatedVar, sandboxVar, sandboxVar, isolatedVar,
        executorVar, sandboxVar
    )
end

-- Generate environment monitoring code
function Environment:generateEnvironmentMonitoring()
    local monitorVar = Utils.generateRandomVariableName()
    local originalVar = Utils.generateRandomVariableName()
    local checkVar = Utils.generateRandomVariableName()
    
    return string.format([[
local %s = {}
local %s = {
    loadstring = loadstring,
    getfenv = getfenv,
    setfenv = setfenv,
    _ENV = _ENV,
    _G = _G
}

local %s = function()
    for name, original in pairs(%s) do
        if _G[name] ~= original then
            -- Environment has been tampered with
            return false
        end
    end
    return true
end

-- Periodic environment integrity check
%s.check = %s
]], 
        monitorVar, originalVar, checkVar, originalVar, monitorVar, checkVar
    )
end

-- Create environment restoration code
function Environment:createEnvironmentRestoration()
    local restoreVar = Utils.generateRandomVariableName()
    local backupVar = Utils.generateRandomVariableName()
    
    return string.format([[
local %s = {
    loadstring = loadstring,
    getfenv = getfenv,
    setfenv = setfenv,
    pcall = pcall,
    xpcall = xpcall,
    type = type,
    pairs = pairs,
    ipairs = ipairs,
    next = next
}

local %s = function()
    for name, func in pairs(%s) do
        if _G[name] ~= func then
            _G[name] = func
        end
    end
end
]], 
        backupVar, restoreVar, backupVar
    )
end

-- Generate complex environment manipulation (Project Madara style)
function Environment:generateComplexEnvironmentManipulation()
    local envVar = Utils.generateRandomVariableName() -- Like twuTsJpSydrWUjTa
    local proxyVar = Utils.generateRandomVariableName()
    local handlerVar = Utils.generateRandomVariableName()
    
    return string.format([[
local %s = _ENV
local %s = {}
local %s = {
    __index = function(t, k)
        if k == "loadstring" then
            return function(code, chunkname)
                if type(code) == "string" then
                    return %s.loadstring(code, chunkname)
                end
            end
        elseif k == "getfenv" then
            return function(level)
                return %s.getfenv(level or 1)
            end
        else
            return %s[k]
        end
    end,
    __newindex = function(t, k, v)
        %s[k] = v
    end
}

_ENV = setmetatable(%s, %s)
]], 
        envVar, proxyVar, handlerVar, envVar, envVar, envVar, envVar, proxyVar, handlerVar
    )
end

-- Get environment manipulation statistics
function Environment:getStats()
    return Utils.deepCopy(self.stats)
end

return Environment
