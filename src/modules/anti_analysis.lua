--[[
    Anti-Analysis and Evasion Module
    
    Implements environment manipulation, dynamic code generation with loadstring,
    and runtime detection capabilities.
    
    Based on Project Madara techniques:
    - Virtual machine detection
    - Environment manipulation
    - Dynamic code generation
]]

local AntiAnalysis = {}
AntiAnalysis.__index = AntiAnalysis

local Utils = require(script.Parent.utils)

-- Create new anti-analysis instance
function AntiAnalysis.new(config)
    local self = setmetatable({}, AntiAnalysis)
    
    self.config = config
    self.stats = {
        vmDetectionChecks = 0,
        environmentManipulations = 0,
        dynamicGenerations = 0,
        antiDebuggingFeatures = 0
    }
    
    return self
end

-- Apply anti-analysis techniques to AST
function AntiAnalysis:applyAntiAnalysis(ast)
    if not ast or type(ast) ~= "table" then
        return ast
    end
    
    -- Add VM detection
    if self.config.enableVMDetection then
        ast = self:addVMDetection(ast)
    end
    
    -- Add dynamic code generation
    if self.config.enableDynamicExecution then
        ast = self:addDynamicCodeGeneration(ast)
    end
    
    -- Add anti-debugging techniques
    ast = self:addAntiDebugging(ast)
    
    return ast
end

-- Add virtual machine detection (Project Madara technique)
function AntiAnalysis:addVMDetection(ast)
    local vmDetectionCode = self:generateVMDetectionCode()
    
    -- Insert VM detection at the beginning of the code
    if ast.type == "program" and ast.body then
        table.insert(ast.body, 1, vmDetectionCode)
    elseif ast.type == "block" and ast.statements then
        table.insert(ast.statements, 1, vmDetectionCode)
    end
    
    self.stats.vmDetectionChecks = self.stats.vmDetectionChecks + 1
    return ast
end

-- Generate VM detection code
function AntiAnalysis:generateVMDetectionCode()
    local checkVar = Utils.generateRandomVariableName()
    local resultVar = Utils.generateRandomVariableName()
    local exitVar = Utils.generateRandomVariableName()
    
    return {
        type = "block",
        statements = {
            {
                type = "local_declaration",
                name = checkVar,
                value = {
                    type = "function_call",
                    name = "pcall",
                    arguments = {
                        {
                            type = "function",
                            parameters = {},
                            body = {
                                type = "block",
                                statements = {
                                    {
                                        type = "return_statement",
                                        value = {
                                            type = "binary_operation",
                                            operator = "and",
                                            left = {
                                                type = "binary_operation",
                                                operator = "~=",
                                                left = {
                                                    type = "function_call",
                                                    name = "type",
                                                    arguments = {{type = "identifier", name = "game"}}
                                                },
                                                right = {type = "literal", value = "userdata"}
                                            },
                                            right = {
                                                type = "binary_operation",
                                                operator = "==",
                                                left = {
                                                    type = "function_call",
                                                    name = "type",
                                                    arguments = {{type = "identifier", name = "_G"}}
                                                },
                                                right = {type = "literal", value = "table"}
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            {
                type = "if_statement",
                condition = {
                    type = "unary_operation",
                    operator = "not",
                    operand = {type = "identifier", name = checkVar}
                },
                then_block = {
                    type = "block",
                    statements = {
                        {
                            type = "return_statement",
                            value = {type = "literal", value = nil}
                        }
                    }
                }
            }
        }
    }
end

-- Add dynamic code generation (Project Madara: loadstring technique)
function AntiAnalysis:addDynamicCodeGeneration(ast)
    if not ast or type(ast) ~= "table" then
        return ast
    end
    
    -- Convert static code blocks to dynamic generation
    if ast.type == "block" and ast.statements then
        for i, statement in ipairs(ast.statements) do
            if math.random() > 0.8 then -- Randomly convert statements
                ast.statements[i] = self:convertToDynamicGeneration(statement)
            end
        end
    end
    
    -- Recursively process child nodes
    for key, value in pairs(ast) do
        if type(value) == "table" then
            ast[key] = self:addDynamicCodeGeneration(value)
        end
    end
    
    return ast
end

-- Convert statement to dynamic code generation
function AntiAnalysis:convertToDynamicGeneration(statement)
    local codeVar = Utils.generateRandomVariableName()
    local generatedCode = self:statementToString(statement)
    
    local dynamicBlock = {
        type = "block",
        statements = {
            {
                type = "local_declaration",
                name = codeVar,
                value = {type = "literal", value = generatedCode}
            },
            {
                type = "expression_statement",
                expression = {
                    type = "function_call",
                    name = "loadstring",
                    arguments = {
                        {type = "identifier", name = codeVar}
                    }
                }
            }
        }
    }
    
    self.stats.dynamicGenerations = self.stats.dynamicGenerations + 1
    return dynamicBlock
end

-- Convert AST statement to string representation
function AntiAnalysis:statementToString(statement)
    -- Simplified AST to string conversion
    if statement.type == "assignment" then
        return string.format("%s = %s", 
            statement.target.name or "var", 
            statement.value.value or "nil")
    elseif statement.type == "function_call" then
        return string.format("%s()", statement.name or "func")
    elseif statement.type == "local_declaration" then
        return string.format("local %s = %s", 
            statement.name or "var", 
            statement.value.value or "nil")
    else
        return "-- dynamic code"
    end
end

-- Add anti-debugging techniques
function AntiAnalysis:addAntiDebugging(ast)
    local antiDebugCode = self:generateAntiDebuggingCode()
    
    -- Insert anti-debugging checks throughout the code
    if ast.type == "block" and ast.statements then
        -- Insert at random positions
        local insertPositions = {}
        for i = 1, math.min(3, #ast.statements) do
            table.insert(insertPositions, math.random(1, #ast.statements + 1))
        end
        
        table.sort(insertPositions, function(a, b) return a > b end)
        
        for _, pos in ipairs(insertPositions) do
            table.insert(ast.statements, pos, antiDebugCode)
        end
    end
    
    self.stats.antiDebuggingFeatures = self.stats.antiDebuggingFeatures + 1
    return ast
end

-- Generate anti-debugging code
function AntiAnalysis:generateAntiDebuggingCode()
    local timeVar1 = Utils.generateRandomVariableName()
    local timeVar2 = Utils.generateRandomVariableName()
    local deltaVar = Utils.generateRandomVariableName()
    
    return {
        type = "block",
        statements = {
            {
                type = "local_declaration",
                name = timeVar1,
                value = {
                    type = "function_call",
                    name = "tick",
                    arguments = {}
                }
            },
            {
                type = "expression_statement",
                expression = {
                    type = "function_call",
                    name = "wait",
                    arguments = {{type = "literal", value = 0.001}}
                }
            },
            {
                type = "local_declaration",
                name = timeVar2,
                value = {
                    type = "function_call",
                    name = "tick",
                    arguments = {}
                }
            },
            {
                type = "local_declaration",
                name = deltaVar,
                value = {
                    type = "binary_operation",
                    operator = "-",
                    left = {type = "identifier", name = timeVar2},
                    right = {type = "identifier", name = timeVar1}
                }
            },
            {
                type = "if_statement",
                condition = {
                    type = "binary_operation",
                    operator = ">",
                    left = {type = "identifier", name = deltaVar},
                    right = {type = "literal", value = 0.1}
                },
                then_block = {
                    type = "block",
                    statements = {
                        {
                            type = "return_statement",
                            value = {type = "literal", value = nil}
                        }
                    }
                }
            }
        }
    }
end

-- Wrap code with anti-analysis protection
function AntiAnalysis:wrapWithAntiAnalysis(code)
    local wrapperVar = Utils.generateRandomVariableName()
    local envVar = Utils.generateRandomVariableName()
    local checkVar = Utils.generateRandomVariableName()
    
    local wrapper = string.format([[
local %s = _ENV
local %s = function()
    -- Environment integrity check
    if type(%s) ~= "table" or not %s.loadstring then
        return
    end
    
    -- Anti-tampering check
    local %s = pcall(function()
        return type(game) == "userdata" and game.GetService
    end)
    
    if not %s then
        return
    end
    
    -- Execute protected code
    local protected_code = %s
    return loadstring(protected_code)()
end

return %s()
]], 
        envVar, wrapperVar, envVar, envVar, checkVar, checkVar, 
        Utils.escapeString(code), wrapperVar
    )
    
    return wrapper
end

-- Generate environment fingerprinting code
function AntiAnalysis:generateEnvironmentFingerprinting()
    local fingerprintVar = Utils.generateRandomVariableName()
    local checksVar = Utils.generateRandomVariableName()
    
    return string.format([[
local %s = {}
local %s = {
    type(game) == "userdata",
    type(_G) == "table",
    type(loadstring) == "function",
    type(getfenv) == "function",
    type(setfenv) == "function"
}

for i, check in ipairs(%s) do
    %s[i] = check
end

-- Verify environment integrity
local integrity = true
for i, result in ipairs(%s) do
    if not result then
        integrity = false
        break
    end
end

if not integrity then
    return
end
]], 
        fingerprintVar, checksVar, checksVar, fingerprintVar, fingerprintVar
    )
end

-- Create conditional execution based on environment
function AntiAnalysis:createConditionalExecution(code)
    local conditionVar = Utils.generateRandomVariableName()
    local executorVar = Utils.generateRandomVariableName()
    
    return string.format([[
local %s = (function()
    -- Runtime environment validation
    local checks = {
        pcall(function() return game:GetService("Players") end),
        type(_G) == "table",
        type(loadstring) == "function"
    }
    
    for _, check in ipairs(checks) do
        if not check then
            return false
        end
    end
    
    return true
end)()

if %s then
    local %s = function()
        %s
    end
    %s()
end
]], 
        conditionVar, conditionVar, executorVar, code, executorVar
    )
end

-- Get anti-analysis statistics
function AntiAnalysis:getStats()
    return Utils.deepCopy(self.stats)
end

return AntiAnalysis
