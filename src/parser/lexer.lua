--[[
    Lua Lexer for Obfuscator
    
    Tokenizes Lua source code for parsing and obfuscation.
    Handles Luau-specific syntax and features.
]]

local Lexer = {}
Lexer.__index = Lexer

-- Token types
local TokenType = {
    -- Literals
    NUMBER = "NUMBER",
    STRING = "STRING",
    BOOLEAN = "BOOLEAN",
    NIL = "NIL",
    
    -- Identifiers and keywords
    IDENTIFIER = "IDENTIFIER",
    KEYWORD = "KEYWORD",
    
    -- Operators
    PLUS = "PLUS",
    MINUS = "MINUS",
    MULTIPLY = "MULTIPLY",
    DIVIDE = "DIVIDE",
    MODULO = "MODULO",
    POWER = "POWER",
    CONCAT = "CONCAT",
    
    -- Comparison
    EQUAL = "EQUAL",
    NOT_EQUAL = "NOT_EQUAL",
    LESS_THAN = "LESS_THAN",
    LESS_EQUAL = "LESS_EQUAL",
    GREATER_THAN = "GREATER_THAN",
    GREATER_EQUAL = "GREATER_EQUAL",
    
    -- Logical
    AND = "AND",
    OR = "OR",
    NOT = "NOT",
    
    -- Assignment
    ASSIGN = "ASSIGN",
    
    -- Punctuation
    SEMICOLON = "SEMICOLON",
    COMMA = "COMMA",
    DOT = "DOT",
    COLON = "COLON",
    
    -- Brackets
    LEFT_PAREN = "LEFT_PAREN",
    RIGHT_PAREN = "RIGHT_PAREN",
    LEFT_BRACKET = "LEFT_BRACKET",
    RIGHT_BRACKET = "RIGHT_BRACKET",
    LEFT_BRACE = "LEFT_BRACE",
    RIGHT_BRACE = "RIGHT_BRACE",
    
    -- Special
    NEWLINE = "NEWLINE",
    EOF = "EOF",
    COMMENT = "COMMENT"
}

-- Lua keywords
local keywords = {
    "and", "break", "do", "else", "elseif", "end", "false", "for",
    "function", "if", "in", "local", "nil", "not", "or", "repeat",
    "return", "then", "true", "until", "while"
}

-- Create keyword lookup table
local keywordSet = {}
for _, keyword in ipairs(keywords) do
    keywordSet[keyword] = true
end

-- Create new lexer instance
function Lexer.new()
    local self = setmetatable({}, Lexer)
    self.TokenType = TokenType
    return self
end

-- Tokenize source code
function Lexer:tokenize(source)
    if type(source) ~= "string" then
        return nil
    end
    
    local tokens = {}
    local position = 1
    local line = 1
    local column = 1
    
    while position <= #source do
        local char = source:sub(position, position)
        
        -- Skip whitespace (except newlines)
        if char:match("%s") and char ~= "\n" then
            if char == "\t" then
                column = column + 4
            else
                column = column + 1
            end
            position = position + 1
            
        -- Handle newlines
        elseif char == "\n" then
            table.insert(tokens, {
                type = TokenType.NEWLINE,
                value = "\n",
                line = line,
                column = column
            })
            line = line + 1
            column = 1
            position = position + 1
            
        -- Handle comments
        elseif char == "-" and source:sub(position + 1, position + 1) == "-" then
            local comment, newPos = self:readComment(source, position)
            table.insert(tokens, {
                type = TokenType.COMMENT,
                value = comment,
                line = line,
                column = column
            })
            position = newPos
            column = column + #comment
            
        -- Handle strings
        elseif char == '"' or char == "'" then
            local string, newPos = self:readString(source, position)
            if string then
                table.insert(tokens, {
                    type = TokenType.STRING,
                    value = string,
                    line = line,
                    column = column
                })
                position = newPos
                column = column + #string + 2 -- +2 for quotes
            else
                return nil -- Invalid string
            end
            
        -- Handle numbers
        elseif char:match("%d") then
            local number, newPos = self:readNumber(source, position)
            table.insert(tokens, {
                type = TokenType.NUMBER,
                value = number,
                line = line,
                column = column
            })
            position = newPos
            column = column + #tostring(number)
            
        -- Handle identifiers and keywords
        elseif char:match("[a-zA-Z_]") then
            local identifier, newPos = self:readIdentifier(source, position)
            local tokenType = keywordSet[identifier] and TokenType.KEYWORD or TokenType.IDENTIFIER
            
            table.insert(tokens, {
                type = tokenType,
                value = identifier,
                line = line,
                column = column
            })
            position = newPos
            column = column + #identifier
            
        -- Handle operators and punctuation
        else
            local operator, newPos = self:readOperator(source, position)
            if operator then
                table.insert(tokens, operator)
                operator.line = line
                operator.column = column
                position = newPos
                column = column + #(operator.value or "")
            else
                -- Unknown character, skip it
                position = position + 1
                column = column + 1
            end
        end
    end
    
    -- Add EOF token
    table.insert(tokens, {
        type = TokenType.EOF,
        value = "",
        line = line,
        column = column
    })
    
    return tokens
end

-- Read comment from source
function Lexer:readComment(source, start)
    local pos = start + 2 -- Skip "--"
    
    -- Check for long comment
    if source:sub(pos, pos) == "[" then
        local level = 0
        local levelStart = pos
        
        -- Count opening brackets
        while source:sub(pos, pos) == "=" do
            level = level + 1
            pos = pos + 1
        end
        
        if source:sub(pos, pos) == "[" then
            pos = pos + 1
            local comment = ""
            
            -- Read until closing brackets
            while pos <= #source do
                if source:sub(pos, pos) == "]" then
                    local closeLevel = 0
                    local closePos = pos + 1
                    
                    while source:sub(closePos, closePos) == "=" do
                        closeLevel = closeLevel + 1
                        closePos = closePos + 1
                    end
                    
                    if closeLevel == level and source:sub(closePos, closePos) == "]" then
                        return comment, closePos + 1
                    end
                end
                
                comment = comment .. source:sub(pos, pos)
                pos = pos + 1
            end
        end
    end
    
    -- Single line comment
    local comment = ""
    while pos <= #source and source:sub(pos, pos) ~= "\n" do
        comment = comment .. source:sub(pos, pos)
        pos = pos + 1
    end
    
    return comment, pos
end

-- Read string literal from source
function Lexer:readString(source, start)
    local quote = source:sub(start, start)
    local pos = start + 1
    local str = ""
    
    while pos <= #source do
        local char = source:sub(pos, pos)
        
        if char == quote then
            return str, pos + 1
        elseif char == "\\" then
            -- Handle escape sequences
            pos = pos + 1
            if pos <= #source then
                local escaped = source:sub(pos, pos)
                if escaped == "n" then
                    str = str .. "\n"
                elseif escaped == "t" then
                    str = str .. "\t"
                elseif escaped == "r" then
                    str = str .. "\r"
                elseif escaped == "\\" then
                    str = str .. "\\"
                elseif escaped == quote then
                    str = str .. quote
                else
                    str = str .. escaped
                end
            end
        else
            str = str .. char
        end
        
        pos = pos + 1
    end
    
    return nil, pos -- Unterminated string
end

-- Read number from source
function Lexer:readNumber(source, start)
    local pos = start
    local numStr = ""
    local hasDecimal = false
    
    while pos <= #source do
        local char = source:sub(pos, pos)
        
        if char:match("%d") then
            numStr = numStr .. char
        elseif char == "." and not hasDecimal then
            hasDecimal = true
            numStr = numStr .. char
        else
            break
        end
        
        pos = pos + 1
    end
    
    return tonumber(numStr), pos
end

-- Read identifier from source
function Lexer:readIdentifier(source, start)
    local pos = start
    local identifier = ""
    
    while pos <= #source do
        local char = source:sub(pos, pos)
        
        if char:match("[a-zA-Z0-9_]") then
            identifier = identifier .. char
            pos = pos + 1
        else
            break
        end
    end
    
    return identifier, pos
end

-- Read operator from source
function Lexer:readOperator(source, start)
    local char = source:sub(start, start)
    local nextChar = source:sub(start + 1, start + 1)
    
    -- Two-character operators
    local twoChar = char .. nextChar
    if twoChar == "==" then
        return {type = TokenType.EQUAL, value = "=="}, start + 2
    elseif twoChar == "~=" then
        return {type = TokenType.NOT_EQUAL, value = "~="}, start + 2
    elseif twoChar == "<=" then
        return {type = TokenType.LESS_EQUAL, value = "<="}, start + 2
    elseif twoChar == ">=" then
        return {type = TokenType.GREATER_EQUAL, value = ">="}, start + 2
    elseif twoChar == ".." then
        return {type = TokenType.CONCAT, value = ".."}, start + 2
    end
    
    -- Single-character operators
    if char == "+" then
        return {type = TokenType.PLUS, value = "+"}, start + 1
    elseif char == "-" then
        return {type = TokenType.MINUS, value = "-"}, start + 1
    elseif char == "*" then
        return {type = TokenType.MULTIPLY, value = "*"}, start + 1
    elseif char == "/" then
        return {type = TokenType.DIVIDE, value = "/"}, start + 1
    elseif char == "%" then
        return {type = TokenType.MODULO, value = "%"}, start + 1
    elseif char == "^" then
        return {type = TokenType.POWER, value = "^"}, start + 1
    elseif char == "=" then
        return {type = TokenType.ASSIGN, value = "="}, start + 1
    elseif char == "<" then
        return {type = TokenType.LESS_THAN, value = "<"}, start + 1
    elseif char == ">" then
        return {type = TokenType.GREATER_THAN, value = ">"}, start + 1
    elseif char == "(" then
        return {type = TokenType.LEFT_PAREN, value = "("}, start + 1
    elseif char == ")" then
        return {type = TokenType.RIGHT_PAREN, value = ")"}, start + 1
    elseif char == "[" then
        return {type = TokenType.LEFT_BRACKET, value = "["}, start + 1
    elseif char == "]" then
        return {type = TokenType.RIGHT_BRACKET, value = "]"}, start + 1
    elseif char == "{" then
        return {type = TokenType.LEFT_BRACE, value = "{"}, start + 1
    elseif char == "}" then
        return {type = TokenType.RIGHT_BRACE, value = "}"}, start + 1
    elseif char == ";" then
        return {type = TokenType.SEMICOLON, value = ";"}, start + 1
    elseif char == "," then
        return {type = TokenType.COMMA, value = ","}, start + 1
    elseif char == "." then
        return {type = TokenType.DOT, value = "."}, start + 1
    elseif char == ":" then
        return {type = TokenType.COLON, value = ":"}, start + 1
    end
    
    return nil, start + 1
end

return Lexer
