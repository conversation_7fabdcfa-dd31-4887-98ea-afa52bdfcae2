--[[
    Abstract Syntax Tree (AST) Parser for Lua
    
    Parses tokens into an AST structure for obfuscation processing.
    Handles Luau-specific syntax and constructs.
]]

local AST = {}
AST.__index = AST

-- AST Node types
local NodeType = {
    PROGRAM = "program",
    BLOCK = "block",
    
    -- Statements
    LOCAL_DECLARATION = "local_declaration",
    ASSIGNMENT = "assignment",
    FUNCTION_DECLARATION = "function_declaration",
    IF_STATEMENT = "if_statement",
    WHILE_LOOP = "while_loop",
    FOR_LOOP = "for_loop",
    REPEAT_LOOP = "repeat_loop",
    RETURN_STATEMENT = "return_statement",
    BREAK_STATEMENT = "break_statement",
    EXPRESSION_STATEMENT = "expression_statement",
    
    -- Expressions
    IDENTIFIER = "identifier",
    LITERAL = "literal",
    BINARY_OPERATION = "binary_operation",
    UNARY_OPERATION = "unary_operation",
    FUNCTION_CALL = "function_call",
    TABLE_CONSTRUCTOR = "table_constructor",
    TABLE_ACCESS = "table_access",
    
    -- Special
    FUNCTION = "function",
    TABLE_FIELD = "table_field"
}

-- Create new AST parser instance
function AST.new()
    local self = setmetatable({}, AST)
    self.NodeType = NodeType
    self.tokens = {}
    self.position = 1
    return self
end

-- Parse tokens into AST
function AST:parse(tokens)
    if not tokens or #tokens == 0 then
        return nil
    end
    
    self.tokens = tokens
    self.position = 1
    
    return self:parseProgram()
end

-- Parse program (top-level)
function AST:parseProgram()
    local statements = {}
    
    while not self:isAtEnd() and not self:check("EOF") do
        -- Skip newlines and comments
        if self:check("NEWLINE") or self:check("COMMENT") then
            self:advance()
        else
            local stmt = self:parseStatement()
            if stmt then
                table.insert(statements, stmt)
            end
        end
    end
    
    return {
        type = NodeType.PROGRAM,
        body = statements
    }
end

-- Parse statement
function AST:parseStatement()
    if self:check("KEYWORD") then
        local keyword = self:peek().value
        
        if keyword == "local" then
            return self:parseLocalDeclaration()
        elseif keyword == "function" then
            return self:parseFunctionDeclaration()
        elseif keyword == "if" then
            return self:parseIfStatement()
        elseif keyword == "while" then
            return self:parseWhileLoop()
        elseif keyword == "for" then
            return self:parseForLoop()
        elseif keyword == "repeat" then
            return self:parseRepeatLoop()
        elseif keyword == "return" then
            return self:parseReturnStatement()
        elseif keyword == "break" then
            self:advance() -- consume 'break'
            return {type = NodeType.BREAK_STATEMENT}
        end
    end
    
    -- Try to parse as assignment or expression statement
    local expr = self:parseExpression()
    if expr then
        -- Check if it's an assignment
        if self:check("ASSIGN") then
            self:advance() -- consume '='
            local value = self:parseExpression()
            return {
                type = NodeType.ASSIGNMENT,
                target = expr,
                value = value
            }
        else
            return {
                type = NodeType.EXPRESSION_STATEMENT,
                expression = expr
            }
        end
    end
    
    -- Skip unknown tokens
    self:advance()
    return nil
end

-- Parse local declaration
function AST:parseLocalDeclaration()
    self:advance() -- consume 'local'
    
    if self:check("KEYWORD") and self:peek().value == "function" then
        return self:parseLocalFunctionDeclaration()
    end
    
    local name = nil
    if self:check("IDENTIFIER") then
        name = self:advance().value
    end
    
    local value = nil
    if self:check("ASSIGN") then
        self:advance() -- consume '='
        value = self:parseExpression()
    end
    
    return {
        type = NodeType.LOCAL_DECLARATION,
        name = name,
        value = value
    }
end

-- Parse function declaration
function AST:parseFunctionDeclaration()
    self:advance() -- consume 'function'
    
    local name = nil
    if self:check("IDENTIFIER") then
        name = self:advance().value
    end
    
    local parameters = {}
    if self:check("LEFT_PAREN") then
        self:advance() -- consume '('
        
        while not self:check("RIGHT_PAREN") and not self:isAtEnd() do
            if self:check("IDENTIFIER") then
                table.insert(parameters, self:advance().value)
            end
            
            if self:check("COMMA") then
                self:advance()
            end
        end
        
        if self:check("RIGHT_PAREN") then
            self:advance() -- consume ')'
        end
    end
    
    local body = self:parseBlock()
    
    if self:check("KEYWORD") and self:peek().value == "end" then
        self:advance() -- consume 'end'
    end
    
    return {
        type = NodeType.FUNCTION_DECLARATION,
        name = name,
        parameters = parameters,
        body = body
    }
end

-- Parse if statement
function AST:parseIfStatement()
    self:advance() -- consume 'if'
    
    local condition = self:parseExpression()
    
    if self:check("KEYWORD") and self:peek().value == "then" then
        self:advance() -- consume 'then'
    end
    
    local thenBlock = self:parseBlock()
    local elseBlock = nil
    
    if self:check("KEYWORD") and self:peek().value == "else" then
        self:advance() -- consume 'else'
        elseBlock = self:parseBlock()
    end
    
    if self:check("KEYWORD") and self:peek().value == "end" then
        self:advance() -- consume 'end'
    end
    
    return {
        type = NodeType.IF_STATEMENT,
        condition = condition,
        then_block = thenBlock,
        else_block = elseBlock
    }
end

-- Parse while loop
function AST:parseWhileLoop()
    self:advance() -- consume 'while'
    
    local condition = self:parseExpression()
    
    if self:check("KEYWORD") and self:peek().value == "do" then
        self:advance() -- consume 'do'
    end
    
    local body = self:parseBlock()
    
    if self:check("KEYWORD") and self:peek().value == "end" then
        self:advance() -- consume 'end'
    end
    
    return {
        type = NodeType.WHILE_LOOP,
        condition = condition,
        body = body
    }
end

-- Parse for loop
function AST:parseForLoop()
    self:advance() -- consume 'for'
    
    local variable = nil
    if self:check("IDENTIFIER") then
        variable = self:advance().value
    end
    
    if self:check("ASSIGN") then
        self:advance() -- consume '='
        
        local start = self:parseExpression()
        
        if self:check("COMMA") then
            self:advance() -- consume ','
        end
        
        local stop = self:parseExpression()
        local step = nil
        
        if self:check("COMMA") then
            self:advance() -- consume ','
            step = self:parseExpression()
        end
        
        if self:check("KEYWORD") and self:peek().value == "do" then
            self:advance() -- consume 'do'
        end
        
        local body = self:parseBlock()
        
        if self:check("KEYWORD") and self:peek().value == "end" then
            self:advance() -- consume 'end'
        end
        
        return {
            type = NodeType.FOR_LOOP,
            variable = variable,
            start = start,
            stop = stop,
            step = step,
            body = body
        }
    end
    
    return nil
end

-- Parse return statement
function AST:parseReturnStatement()
    self:advance() -- consume 'return'
    
    local value = nil
    if not self:check("NEWLINE") and not self:check("EOF") and 
       not (self:check("KEYWORD") and self:peek().value == "end") then
        value = self:parseExpression()
    end
    
    return {
        type = NodeType.RETURN_STATEMENT,
        value = value
    }
end

-- Parse block of statements
function AST:parseBlock()
    local statements = {}
    
    while not self:isAtEnd() and not self:check("EOF") do
        if self:check("KEYWORD") then
            local keyword = self:peek().value
            if keyword == "end" or keyword == "else" or keyword == "elseif" then
                break
            end
        end
        
        if self:check("NEWLINE") or self:check("COMMENT") then
            self:advance()
        else
            local stmt = self:parseStatement()
            if stmt then
                table.insert(statements, stmt)
            end
        end
    end
    
    return {
        type = NodeType.BLOCK,
        statements = statements
    }
end

-- Parse expression
function AST:parseExpression()
    return self:parseOrExpression()
end

-- Parse OR expression
function AST:parseOrExpression()
    local left = self:parseAndExpression()
    
    while self:check("KEYWORD") and self:peek().value == "or" do
        local operator = self:advance().value
        local right = self:parseAndExpression()
        left = {
            type = NodeType.BINARY_OPERATION,
            operator = operator,
            left = left,
            right = right
        }
    end
    
    return left
end

-- Parse AND expression
function AST:parseAndExpression()
    local left = self:parseEqualityExpression()
    
    while self:check("KEYWORD") and self:peek().value == "and" do
        local operator = self:advance().value
        local right = self:parseEqualityExpression()
        left = {
            type = NodeType.BINARY_OPERATION,
            operator = operator,
            left = left,
            right = right
        }
    end
    
    return left
end

-- Parse equality expression
function AST:parseEqualityExpression()
    local left = self:parseComparisonExpression()
    
    while self:check("EQUAL") or self:check("NOT_EQUAL") do
        local operator = self:advance().value
        local right = self:parseComparisonExpression()
        left = {
            type = NodeType.BINARY_OPERATION,
            operator = operator,
            left = left,
            right = right
        }
    end
    
    return left
end

-- Parse comparison expression
function AST:parseComparisonExpression()
    local left = self:parseArithmeticExpression()
    
    while self:check("LESS_THAN") or self:check("LESS_EQUAL") or 
          self:check("GREATER_THAN") or self:check("GREATER_EQUAL") do
        local operator = self:advance().value
        local right = self:parseArithmeticExpression()
        left = {
            type = NodeType.BINARY_OPERATION,
            operator = operator,
            left = left,
            right = right
        }
    end
    
    return left
end

-- Parse arithmetic expression
function AST:parseArithmeticExpression()
    local left = self:parseTermExpression()
    
    while self:check("PLUS") or self:check("MINUS") or self:check("CONCAT") do
        local operator = self:advance().value
        local right = self:parseTermExpression()
        left = {
            type = NodeType.BINARY_OPERATION,
            operator = operator,
            left = left,
            right = right
        }
    end
    
    return left
end

-- Parse term expression
function AST:parseTermExpression()
    local left = self:parsePrimaryExpression()
    
    while self:check("MULTIPLY") or self:check("DIVIDE") or self:check("MODULO") do
        local operator = self:advance().value
        local right = self:parsePrimaryExpression()
        left = {
            type = NodeType.BINARY_OPERATION,
            operator = operator,
            left = left,
            right = right
        }
    end
    
    return left
end

-- Parse primary expression
function AST:parsePrimaryExpression()
    if self:check("NUMBER") then
        return {
            type = NodeType.LITERAL,
            value = self:advance().value
        }
    elseif self:check("STRING") then
        return {
            type = NodeType.LITERAL,
            value = self:advance().value
        }
    elseif self:check("KEYWORD") then
        local keyword = self:peek().value
        if keyword == "true" or keyword == "false" or keyword == "nil" then
            return {
                type = NodeType.LITERAL,
                value = self:advance().value
            }
        end
    elseif self:check("IDENTIFIER") then
        local name = self:advance().value
        
        -- Check for function call
        if self:check("LEFT_PAREN") then
            self:advance() -- consume '('
            local arguments = {}
            
            while not self:check("RIGHT_PAREN") and not self:isAtEnd() do
                table.insert(arguments, self:parseExpression())
                
                if self:check("COMMA") then
                    self:advance()
                end
            end
            
            if self:check("RIGHT_PAREN") then
                self:advance() -- consume ')'
            end
            
            return {
                type = NodeType.FUNCTION_CALL,
                name = name,
                arguments = arguments
            }
        else
            return {
                type = NodeType.IDENTIFIER,
                name = name
            }
        end
    elseif self:check("LEFT_PAREN") then
        self:advance() -- consume '('
        local expr = self:parseExpression()
        if self:check("RIGHT_PAREN") then
            self:advance() -- consume ')'
        end
        return expr
    end
    
    return nil
end

-- Helper methods
function AST:peek()
    if self.position <= #self.tokens then
        return self.tokens[self.position]
    end
    return {type = "EOF", value = ""}
end

function AST:advance()
    if not self:isAtEnd() then
        self.position = self.position + 1
    end
    return self.tokens[self.position - 1]
end

function AST:check(tokenType)
    if self:isAtEnd() then
        return false
    end
    return self:peek().type == tokenType
end

function AST:isAtEnd()
    return self.position > #self.tokens
end

return AST
