--[[
    Luau Advanced Obfuscator - Main Entry Point
    
    This is the main orchestration engine that coordinates all obfuscation modules
    and provides a unified API for code obfuscation.
    
    Based on analysis of Project Madara obfuscation techniques.
]]

local Obfuscator = {}
Obfuscator.__index = Obfuscator

-- Module imports
local StringObfuscator = require(script.Parent.modules.string_obfuscator)
local StructureObfuscator = require(script.Parent.modules.structure_obfuscator)
local AntiAnalysis = require(script.Parent.modules.anti_analysis)
local Cipher = require(script.Parent.modules.cipher)
local Environment = require(script.Parent.modules.environment)
local Utils = require(script.Parent.modules.utils)
local Lexer = require(script.Parent.parser.lexer)
local AST = require(script.Parent.parser.ast)
local Generator = require(script.Parent.output.generator)

-- Default configuration
local DEFAULT_CONFIG = {
    stringObfuscation = true,
    structureObfuscation = true,
    antiAnalysis = true,
    environmentManipulation = true,
    compressionLevel = "high", -- "low", "medium", "high"
    preserveComments = false,
    preserveWhitespace = false,
    enableVMDetection = true,
    enableDynamicExecution = true,
    customCharacterSet = nil, -- Use default if nil
    obfuscationSeed = nil, -- Random if nil
}

-- Create new obfuscator instance
function Obfuscator.new(config)
    local self = setmetatable({}, Obfuscator)
    
    -- Merge config with defaults
    self.config = Utils.mergeConfig(DEFAULT_CONFIG, config or {})
    
    -- Initialize random seed
    if self.config.obfuscationSeed then
        math.randomseed(self.config.obfuscationSeed)
    else
        math.randomseed(tick())
    end
    
    -- Initialize modules
    self.stringObfuscator = StringObfuscator.new(self.config)
    self.structureObfuscator = StructureObfuscator.new(self.config)
    self.antiAnalysis = AntiAnalysis.new(self.config)
    self.cipher = Cipher.new(self.config)
    self.environment = Environment.new(self.config)
    self.lexer = Lexer.new()
    self.ast = AST.new()
    self.generator = Generator.new(self.config)
    
    return self
end

-- Main obfuscation function
function Obfuscator:obfuscate(sourceCode)
    if type(sourceCode) ~= "string" then
        error("Source code must be a string")
    end
    
    if #sourceCode == 0 then
        return ""
    end
    
    -- Phase 1: Lexical Analysis
    local tokens = self.lexer:tokenize(sourceCode)
    if not tokens then
        error("Failed to tokenize source code")
    end
    
    -- Phase 2: Parse into AST
    local ast = self.ast:parse(tokens)
    if not ast then
        error("Failed to parse source code into AST")
    end
    
    -- Phase 3: Apply obfuscation transformations
    ast = self:applyObfuscations(ast)
    
    -- Phase 4: Generate obfuscated code
    local obfuscatedCode = self.generator:generate(ast)
    
    -- Phase 5: Apply final transformations
    obfuscatedCode = self:applyFinalTransformations(obfuscatedCode)
    
    return obfuscatedCode
end

-- Apply all obfuscation transformations to the AST
function Obfuscator:applyObfuscations(ast)
    -- String obfuscation
    if self.config.stringObfuscation then
        ast = self.stringObfuscator:obfuscateStrings(ast)
    end
    
    -- Structure obfuscation
    if self.config.structureObfuscation then
        ast = self.structureObfuscator:obfuscateStructure(ast)
    end
    
    -- Anti-analysis techniques
    if self.config.antiAnalysis then
        ast = self.antiAnalysis:applyAntiAnalysis(ast)
    end
    
    -- Environment manipulation
    if self.config.environmentManipulation then
        ast = self.environment:applyEnvironmentManipulation(ast)
    end
    
    return ast
end

-- Apply final transformations to the generated code
function Obfuscator:applyFinalTransformations(code)
    -- Apply cipher encoding
    code = self.cipher:encodeCode(code)
    
    -- Compress to single line if requested
    if self.config.compressionLevel == "high" then
        code = self:compressToSingleLine(code)
    end
    
    -- Add anti-analysis wrapper
    if self.config.antiAnalysis then
        code = self.antiAnalysis:wrapWithAntiAnalysis(code)
    end
    
    return code
end

-- Compress code to single line (Project Madara technique)
function Obfuscator:compressToSingleLine(code)
    -- Remove all whitespace and newlines while preserving string literals
    local compressed = ""
    local inString = false
    local stringChar = nil
    local escaped = false
    
    for i = 1, #code do
        local char = code:sub(i, i)
        
        if escaped then
            compressed = compressed .. char
            escaped = false
        elseif char == "\\" and inString then
            compressed = compressed .. char
            escaped = true
        elseif (char == '"' or char == "'") and not inString then
            inString = true
            stringChar = char
            compressed = compressed .. char
        elseif char == stringChar and inString then
            inString = false
            stringChar = nil
            compressed = compressed .. char
        elseif inString then
            compressed = compressed .. char
        elseif char:match("%s") then
            -- Skip whitespace outside strings
        else
            compressed = compressed .. char
        end
    end
    
    return compressed
end

-- Utility function to get obfuscation statistics
function Obfuscator:getStats()
    return {
        stringObfuscations = self.stringObfuscator:getStats(),
        structureObfuscations = self.structureObfuscator:getStats(),
        antiAnalysisFeatures = self.antiAnalysis:getStats(),
        cipherOperations = self.cipher:getStats(),
        environmentManipulations = self.environment:getStats(),
    }
end

return Obfuscator
