--[[
    Example Usage of Luau Advanced Obfuscator
    
    This script demonstrates how to use the obfuscator with various
    configuration options and shows the results.
]]

-- Import the obfuscator
local Obfuscator = require("src.main")

-- Example 1: Basic obfuscation
print("=== Example 1: Basic Obfuscation ===")

local basicObfuscator = Obfuscator.new({
    stringObfuscation = true,
    structureObfuscation = true,
    compressionLevel = "medium"
})

local basicCode = [[
local function greet(name)
    local message = "Hello, " .. name .. "!"
    print(message)
    return message
end

greet("World")
]]

print("Original code:")
print(basicCode)
print("\nObfuscated code:")
local basicObfuscated = basicObfuscator:obfuscate(basicCode)
print(basicObfuscated)

-- Example 2: High-security obfuscation
print("\n=== Example 2: High-Security Obfuscation ===")

local secureObfuscator = Obfuscator.new({
    stringObfuscation = true,
    structureObfuscation = true,
    antiAnalysis = true,
    environmentManipulation = true,
    enableVMDetection = true,
    enableDynamicExecution = true,
    compressionLevel = "high"
})

local secureCode = [[
local Players = game:GetService("Players")
local player = Players.LocalPlayer

local function checkPermissions(userId)
    local adminList = {123456, 789012, 345678}
    
    for _, adminId in ipairs(adminList) do
        if userId == adminId then
            return true
        end
    end
    
    return false
end

if checkPermissions(player.UserId) then
    print("Access granted!")
else
    print("Access denied!")
end
]]

print("Original code:")
print(secureCode)
print("\nObfuscated code:")
local secureObfuscated = secureObfuscator:obfuscate(secureCode)
print(secureObfuscated)

-- Example 3: Custom configuration
print("\n=== Example 3: Custom Configuration ===")

local customObfuscator = Obfuscator.new({
    stringObfuscation = true,
    structureObfuscation = false,  -- Disable structure obfuscation
    antiAnalysis = false,          -- Disable anti-analysis
    environmentManipulation = false, -- Disable environment manipulation
    compressionLevel = "low",      -- Low compression for readability
    preserveComments = false,
    preserveWhitespace = true
})

local customCode = [[
-- This is a comment
local data = {
    name = "Test Data",
    values = {1, 2, 3, 4, 5},
    active = true
}

for i, value in ipairs(data.values) do
    print("Value " .. i .. ": " .. value)
end
]]

print("Original code:")
print(customCode)
print("\nObfuscated code (strings only):")
local customObfuscated = customObfuscator:obfuscate(customCode)
print(customObfuscated)

-- Example 4: Statistics and analysis
print("\n=== Example 4: Obfuscation Statistics ===")

local statsObfuscator = Obfuscator.new({
    stringObfuscation = true,
    structureObfuscation = true,
    antiAnalysis = true,
    environmentManipulation = true,
    compressionLevel = "high"
})

local complexCode = [[
local HttpService = game:GetService("HttpService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

local config = {
    apiUrl = "https://api.example.com/data",
    timeout = 30,
    retries = 3
}

local function makeRequest(endpoint, data)
    local url = config.apiUrl .. "/" .. endpoint
    local jsonData = HttpService:JSONEncode(data)
    
    for attempt = 1, config.retries do
        local success, response = pcall(function()
            return HttpService:PostAsync(url, jsonData, Enum.HttpContentType.ApplicationJson)
        end)
        
        if success then
            return HttpService:JSONDecode(response)
        else
            print("Request failed, attempt " .. attempt .. "/" .. config.retries)
            wait(1)
        end
    end
    
    error("All request attempts failed")
end

local function processData(inputData)
    local processed = {}
    
    for key, value in pairs(inputData) do
        if type(value) == "string" then
            processed[key] = value:upper()
        elseif type(value) == "number" then
            processed[key] = value * 2
        else
            processed[key] = tostring(value)
        end
    end
    
    return processed
end

-- Main execution
local testData = {
    message = "hello world",
    count = 42,
    enabled = true
}

local result = makeRequest("process", testData)
local finalData = processData(result)

print("Processing complete:", HttpService:JSONEncode(finalData))
]]

print("Obfuscating complex code...")
local complexObfuscated = statsObfuscator:obfuscate(complexCode)

-- Get and display statistics
local stats = statsObfuscator:getStats()
print("\nObfuscation Statistics:")
print("- Strings obfuscated:", stats.stringObfuscations.stringsObfuscated)
print("- Encoding layers:", stats.stringObfuscations.encodingLayers)
print("- Dynamic constructions:", stats.stringObfuscations.dynamicConstructions)
print("- Variables renamed:", stats.structureObfuscations.variablesRenamed)
print("- Functions obfuscated:", stats.structureObfuscations.functionsObfuscated)
print("- Control flow complexity:", stats.structureObfuscations.controlFlowComplexity)
print("- VM detection checks:", stats.antiAnalysisFeatures.vmDetectionChecks)
print("- Environment hijacks:", stats.environmentManipulations.environmentHijacks)

print("\nOriginal code length:", #complexCode, "characters")
print("Obfuscated code length:", #complexObfuscated, "characters")
print("Size increase:", string.format("%.1f%%", (#complexObfuscated / #complexCode - 1) * 100))

-- Example 5: Roblox-specific obfuscation
print("\n=== Example 5: Roblox-Specific Code ===")

local robloxObfuscator = Obfuscator.new({
    stringObfuscation = true,
    structureObfuscation = true,
    antiAnalysis = true,
    environmentManipulation = true,
    enableVMDetection = true,
    compressionLevel = "high"
})

local robloxCode = [[
local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer
local character = player.Character or player.CharacterAdded:Wait()
local humanoid = character:WaitForChild("Humanoid")

local isFlying = false
local flySpeed = 50

local function toggleFly()
    isFlying = not isFlying
    
    if isFlying then
        local bodyVelocity = Instance.new("BodyVelocity")
        bodyVelocity.MaxForce = Vector3.new(4000, 4000, 4000)
        bodyVelocity.Velocity = Vector3.new(0, 0, 0)
        bodyVelocity.Parent = character.HumanoidRootPart
        
        local connection
        connection = RunService.Heartbeat:Connect(function()
            if not isFlying then
                bodyVelocity:Destroy()
                connection:Disconnect()
                return
            end
            
            local camera = workspace.CurrentCamera
            local moveVector = humanoid.MoveDirection
            local lookVector = camera.CFrame.LookVector
            
            bodyVelocity.Velocity = (lookVector * moveVector.Z + camera.CFrame.RightVector * moveVector.X) * flySpeed
        end)
    end
end

UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end
    
    if input.KeyCode == Enum.KeyCode.F then
        toggleFly()
    end
end)

print("Fly script loaded! Press F to toggle flying.")
]]

print("Obfuscating Roblox-specific code...")
local robloxObfuscated = robloxObfuscator:obfuscate(robloxCode)

print("Original Roblox code length:", #robloxCode, "characters")
print("Obfuscated Roblox code length:", #robloxObfuscated, "characters")

-- Show a sample of the obfuscated code (first 500 characters)
print("\nSample of obfuscated Roblox code:")
print(robloxObfuscated:sub(1, 500) .. "...")

print("\n=== Obfuscation Examples Complete ===")
print("The Luau Advanced Obfuscator successfully processed all examples!")
print("Check the output above to see the various obfuscation techniques in action.")

-- Return the obfuscator for further use
return {
    Obfuscator = Obfuscator,
    examples = {
        basic = basicObfuscated,
        secure = secureObfuscated,
        custom = customObfuscated,
        complex = complexObfuscated,
        roblox = robloxObfuscated
    }
}
