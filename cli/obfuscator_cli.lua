#!/usr/bin/env lua
--[[
    Luau Advanced Obfuscator - Command Line Interface
    
    A practical CLI for obfuscating Lua/Luau code files.
    
    Usage:
        lua cli/obfuscator_cli.lua [options] <input_file> [output_file]
        
    Options:
        --help, -h              Show this help message
        --config, -c <file>     Use configuration file
        --preset <name>         Use preset configuration (basic, secure, minimal)
        --no-strings           Disable string obfuscation
        --no-structure         Disable structure obfuscation
        --no-anti-analysis     Disable anti-analysis features
        --no-environment       Disable environment manipulation
        --compression <level>   Set compression level (low, medium, high)
        --stats                Show obfuscation statistics
        --test                 Run test suite
        --example              Run example demonstrations
]]

-- Add the src directory to the package path for module loading
local function addToPath(path)
    package.path = package.path .. ";" .. path .. "/?.lua"
end

-- Get the directory of this script
local scriptDir = debug.getinfo(1, "S").source:match("@(.*/)")
if scriptDir then
    local projectRoot = scriptDir:gsub("/cli/?$", "")
    addToPath(projectRoot .. "/src")
    addToPath(projectRoot .. "/src/modules")
    addToPath(projectRoot .. "/src/parser")
    addToPath(projectRoot .. "/src/output")
    addToPath(projectRoot .. "/tests")
else
    -- Fallback for relative paths
    addToPath("src")
    addToPath("src/modules")
    addToPath("src/parser")
    addToPath("src/output")
    addToPath("tests")
end

-- CLI Module
local CLI = {}

-- Default configuration presets
CLI.presets = {
    basic = {
        stringObfuscation = true,
        structureObfuscation = true,
        antiAnalysis = false,
        environmentManipulation = false,
        compressionLevel = "medium"
    },
    secure = {
        stringObfuscation = true,
        structureObfuscation = true,
        antiAnalysis = true,
        environmentManipulation = true,
        enableVMDetection = true,
        enableDynamicExecution = true,
        compressionLevel = "high"
    },
    minimal = {
        stringObfuscation = true,
        structureObfuscation = false,
        antiAnalysis = false,
        environmentManipulation = false,
        compressionLevel = "low"
    }
}

-- Parse command line arguments
function CLI.parseArgs(args)
    local options = {
        inputFile = nil,
        outputFile = nil,
        configFile = nil,
        preset = "basic",
        showHelp = false,
        showStats = false,
        runTests = false,
        runExample = false,
        config = {}
    }
    
    local i = 1
    while i <= #args do
        local arg = args[i]
        
        if arg == "--help" or arg == "-h" then
            options.showHelp = true
        elseif arg == "--config" or arg == "-c" then
            i = i + 1
            options.configFile = args[i]
        elseif arg == "--preset" then
            i = i + 1
            options.preset = args[i]
        elseif arg == "--no-strings" then
            options.config.stringObfuscation = false
        elseif arg == "--no-structure" then
            options.config.structureObfuscation = false
        elseif arg == "--no-anti-analysis" then
            options.config.antiAnalysis = false
        elseif arg == "--no-environment" then
            options.config.environmentManipulation = false
        elseif arg == "--compression" then
            i = i + 1
            options.config.compressionLevel = args[i]
        elseif arg == "--stats" then
            options.showStats = true
        elseif arg == "--test" then
            options.runTests = true
        elseif arg == "--example" then
            options.runExample = true
        elseif not arg:match("^%-") then
            if not options.inputFile then
                options.inputFile = arg
            elseif not options.outputFile then
                options.outputFile = arg
            end
        end
        
        i = i + 1
    end
    
    return options
end

-- Show help message
function CLI.showHelp()
    print([[
Luau Advanced Obfuscator - Command Line Interface

USAGE:
    lua cli/obfuscator_cli.lua [OPTIONS] <input_file> [output_file]

ARGUMENTS:
    <input_file>        Input Lua file to obfuscate
    [output_file]       Output file (default: input_obfuscated.lua)

OPTIONS:
    --help, -h              Show this help message
    --config, -c <file>     Use configuration file
    --preset <name>         Use preset configuration
                           Available presets: basic, secure, minimal
    --no-strings           Disable string obfuscation
    --no-structure         Disable structure obfuscation
    --no-anti-analysis     Disable anti-analysis features
    --no-environment       Disable environment manipulation
    --compression <level>   Set compression level (low, medium, high)
    --stats                Show obfuscation statistics
    --test                 Run test suite
    --example              Run example demonstrations

PRESETS:
    basic      - String and structure obfuscation (default)
    secure     - Full obfuscation with anti-analysis
    minimal    - String obfuscation only

EXAMPLES:
    # Basic obfuscation
    lua cli/obfuscator_cli.lua input.lua

    # Secure obfuscation with statistics
    lua cli/obfuscator_cli.lua --preset secure --stats input.lua output.lua

    # Custom configuration
    lua cli/obfuscator_cli.lua --no-anti-analysis --compression high input.lua

    # Run tests
    lua cli/obfuscator_cli.lua --test

    # Run examples
    lua cli/obfuscator_cli.lua --example
]])
end

-- Read file contents
function CLI.readFile(filename)
    local file = io.open(filename, "r")
    if not file then
        return nil, "Could not open file: " .. filename
    end
    
    local content = file:read("*all")
    file:close()
    return content
end

-- Write file contents
function CLI.writeFile(filename, content)
    local file = io.open(filename, "w")
    if not file then
        return false, "Could not create file: " .. filename
    end
    
    file:write(content)
    file:close()
    return true
end

-- Load configuration from file
function CLI.loadConfig(filename)
    local content, err = CLI.readFile(filename)
    if not content then
        return nil, err
    end
    
    -- Simple JSON-like config parser
    local config = {}
    for line in content:gmatch("[^\r\n]+") do
        line = line:match("^%s*(.-)%s*$") -- trim whitespace
        if line and not line:match("^#") and not line:match("^//") then
            local key, value = line:match("([%w_]+)%s*=%s*(.+)")
            if key and value then
                -- Parse value
                if value == "true" then
                    config[key] = true
                elseif value == "false" then
                    config[key] = false
                elseif value:match("^%d+$") then
                    config[key] = tonumber(value)
                elseif value:match('^".*"$') then
                    config[key] = value:sub(2, -2) -- remove quotes
                else
                    config[key] = value
                end
            end
        end
    end
    
    return config
end

-- Merge configurations
function CLI.mergeConfig(base, override)
    local result = {}
    
    -- Copy base config
    for k, v in pairs(base) do
        result[k] = v
    end
    
    -- Override with new values
    for k, v in pairs(override) do
        result[k] = v
    end
    
    return result
end

-- Run obfuscation
function CLI.obfuscate(inputFile, outputFile, config)
    print("🔧 Loading obfuscator...")
    
    -- Load the obfuscator (with error handling for module loading)
    local success, Obfuscator = pcall(require, "main")
    if not success then
        return false, "Failed to load obfuscator: " .. tostring(Obfuscator)
    end
    
    print("📖 Reading input file: " .. inputFile)
    local sourceCode, err = CLI.readFile(inputFile)
    if not sourceCode then
        return false, err
    end
    
    print("⚙️  Creating obfuscator with configuration...")
    local obfuscator = Obfuscator.new(config)
    
    print("🔐 Obfuscating code...")
    local startTime = os.clock()
    local obfuscatedCode = obfuscator:obfuscate(sourceCode)
    local endTime = os.clock()
    
    print("💾 Writing output file: " .. outputFile)
    local writeSuccess, writeErr = CLI.writeFile(outputFile, obfuscatedCode)
    if not writeSuccess then
        return false, writeErr
    end
    
    local stats = obfuscator:getStats()
    local processingTime = endTime - startTime
    
    print("✅ Obfuscation complete!")
    print(string.format("   📊 Processing time: %.3f seconds", processingTime))
    print(string.format("   📏 Original size: %d characters", #sourceCode))
    print(string.format("   📏 Obfuscated size: %d characters", #obfuscatedCode))
    print(string.format("   📈 Size increase: %.1f%%", (#obfuscatedCode / #sourceCode - 1) * 100))
    
    return true, stats
end

-- Show statistics
function CLI.showStats(stats)
    print("\n📊 Obfuscation Statistics:")
    print("   🔤 Strings obfuscated:", stats.stringObfuscations.stringsObfuscated)
    print("   🔄 Encoding layers:", stats.stringObfuscations.encodingLayers)
    print("   🏗️  Dynamic constructions:", stats.stringObfuscations.dynamicConstructions)
    print("   🏷️  Variables renamed:", stats.structureObfuscations.variablesRenamed)
    print("   🔧 Functions obfuscated:", stats.structureObfuscations.functionsObfuscated)
    print("   🌊 Control flow complexity:", stats.structureObfuscations.controlFlowComplexity)
    print("   🛡️  VM detection checks:", stats.antiAnalysisFeatures.vmDetectionChecks)
    print("   🌍 Environment hijacks:", stats.environmentManipulations.environmentHijacks)
end

-- Main CLI function
function CLI.main(args)
    local options = CLI.parseArgs(args)
    
    if options.showHelp then
        CLI.showHelp()
        return true
    end
    
    if options.runTests then
        print("🧪 Running test suite...")
        -- We'll implement this after fixing the module loading
        print("⚠️  Test suite integration coming soon...")
        return true
    end
    
    if options.runExample then
        print("🎯 Running example demonstrations...")
        -- We'll implement this after fixing the module loading
        print("⚠️  Example demonstrations coming soon...")
        return true
    end
    
    if not options.inputFile then
        print("❌ Error: Input file required")
        print("Use --help for usage information")
        return false
    end
    
    -- Generate output filename if not provided
    if not options.outputFile then
        local name, ext = options.inputFile:match("^(.+)%.([^%.]+)$")
        if name and ext then
            options.outputFile = name .. "_obfuscated." .. ext
        else
            options.outputFile = options.inputFile .. "_obfuscated"
        end
    end
    
    -- Build configuration
    local config = CLI.presets[options.preset] or CLI.presets.basic
    
    -- Load config file if specified
    if options.configFile then
        local fileConfig, err = CLI.loadConfig(options.configFile)
        if fileConfig then
            config = CLI.mergeConfig(config, fileConfig)
        else
            print("⚠️  Warning: Could not load config file: " .. err)
        end
    end
    
    -- Apply command line overrides
    config = CLI.mergeConfig(config, options.config)
    
    print("🚀 Starting obfuscation process...")
    print("   📁 Input: " .. options.inputFile)
    print("   📁 Output: " .. options.outputFile)
    print("   ⚙️  Preset: " .. options.preset)
    
    local success, result = CLI.obfuscate(options.inputFile, options.outputFile, config)
    
    if success then
        if options.showStats and result then
            CLI.showStats(result)
        end
        return true
    else
        print("❌ Error: " .. result)
        return false
    end
end

-- Run CLI if this file is executed directly
if arg and arg[0] and arg[0]:match("obfuscator_cli%.lua$") then
    local success = CLI.main(arg)
    os.exit(success and 0 or 1)
end

return CLI
