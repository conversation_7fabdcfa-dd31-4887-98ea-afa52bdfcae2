#!/bin/bash

# Luau Obfuscator Discord Bot Startup Script

echo "🤖 Starting Luau Obfuscator Discord Bot..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 16.0.0 or higher."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node --version | cut -d'v' -f2)
REQUIRED_VERSION="16.0.0"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    echo "❌ Node.js version $NODE_VERSION is too old. Please install Node.js 16.0.0 or higher."
    exit 1
fi

echo "✅ Node.js version: $NODE_VERSION"

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ .env file not found. Please create one based on .env.example"
    echo "📝 Copy .env.example to .env and fill in your Discord bot credentials."
    exit 1
fi

echo "✅ Environment file found"

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install dependencies"
        exit 1
    fi
fi

echo "✅ Dependencies ready"

# Run tests first
echo "🧪 Running tests..."
node test.js
if [ $? -ne 0 ]; then
    echo "❌ Tests failed. Please check the obfuscator implementation."
    exit 1
fi

echo "✅ Tests passed"

# Start the bot
echo "🚀 Starting Discord bot..."
echo "📝 Use Ctrl+C to stop the bot"
echo "🔗 Invite the bot to your server and use /obf command"
echo ""

node bot.js
