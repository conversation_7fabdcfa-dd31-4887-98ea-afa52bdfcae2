-- Sam<PERSON> script for testing the Discord bot
local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")

local player = Players.LocalPlayer
local character = player.Character or player.CharacterAdded:Wait()

local function greetPlayer(playerName)
    local message = "Welcome to the game, " .. playerName .. "!"
    print(message)
    return message
end

local function checkPermissions(userId)
    local adminList = {123456789, 987654321, 555666777}
    
    for _, adminId in ipairs(adminList) do
        if userId == adminId then
            return true
        end
    end
    
    return false
end

local function onKeyPress(key)
    if key.KeyCode == Enum.KeyCode.E then
        if checkPermissions(player.UserId) then
            print("Admin command executed!")
        else
            print("Access denied - insufficient permissions")
        end
    end
end

-- Main execution
greetPlayer(player.Name)

UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if not gameProcessed then
        onKeyPress(input)
    end
end)

print("<PERSON><PERSON><PERSON> loaded successfully!")
