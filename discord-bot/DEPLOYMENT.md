# Discord Bot Deployment Guide

This guide will help you deploy the Luau Obfuscator Discord Bot to your server or Discord guild.

## Prerequisites

### System Requirements
- **Node.js**: Version 16.0.0 or higher
- **npm**: Comes with Node.js
- **Operating System**: Windows, macOS, or Linux
- **Memory**: At least 512MB RAM
- **Storage**: 100MB free space

### Discord Requirements
- Discord account
- Server with "Manage Server" permissions (to add the bot)
- Basic understanding of Discord bots

## Step 1: Create Discord Application

### 1.1 Create Application
1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Click "New Application"
3. Enter name: "Luau Obfuscator Bot"
4. Click "Create"

### 1.2 Create Bot
1. Go to "Bot" section in the left sidebar
2. Click "Add Bot"
3. Confirm by clicking "Yes, do it!"
4. **Copy the bot token** (you'll need this later)
5. Under "Privileged Gateway Intents", enable:
   - ✅ Message Content Intent (if needed)

### 1.3 Get Application ID
1. Go to "General Information" section
2. **Copy the Application ID** (this is your CLIENT_ID)

### 1.4 Set Bot Permissions
1. Go to "OAuth2" → "URL Generator"
2. Select scopes:
   - ✅ `bot`
   - ✅ `applications.commands`
3. Select bot permissions:
   - ✅ Send Messages
   - ✅ Use Slash Commands
   - ✅ Attach Files
   - ✅ Read Message History
4. **Copy the generated URL** for inviting the bot

## Step 2: Setup Bot Files

### 2.1 Download/Clone Files
Ensure you have all the bot files in a directory:
```
discord-bot/
├── bot.js
├── obfuscator.js
├── test.js
├── package.json
├── .env.example
├── start.sh
├── sample.lua
└── README.md
```

### 2.2 Install Dependencies
```bash
cd discord-bot
npm install
```

### 2.3 Configure Environment
1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` file with your credentials:
   ```env
   DISCORD_TOKEN=your_bot_token_here
   CLIENT_ID=your_application_id_here
   
   # Optional settings (defaults shown)
   MAX_FILE_SIZE=1048576
   ALLOWED_EXTENSIONS=.lua,.luau,.txt
   RATE_LIMIT_REQUESTS=5
   RATE_LIMIT_WINDOW=60000
   ```

## Step 3: Test the Bot

### 3.1 Run Tests
```bash
node test.js
```

You should see output like:
```
🚀 Starting Luau Obfuscator Tests
✅ All tests completed successfully!
```

### 3.2 Test Bot Locally
```bash
node bot.js
```

You should see:
```
🔄 Registering slash commands...
✅ Slash commands registered successfully
🤖 Bot logged in as YourBotName#1234
🔧 Serving 0 guilds
```

## Step 4: Invite Bot to Server

### 4.1 Use Invite URL
1. Use the URL generated in Step 1.4
2. Select your server
3. Authorize the bot
4. Complete the captcha if prompted

### 4.2 Verify Bot Presence
- Bot should appear in your server's member list
- Bot should be online (green status)

## Step 5: Test Commands

### 5.1 Test Help Command
Type in any channel: `/help`

Expected response: Help embed with bot information

### 5.2 Test Presets Command
Type: `/presets`

Expected response: Preset information embed

### 5.3 Test Obfuscation
1. Type: `/obf`
2. Upload the `sample.lua` file
3. Choose preset: "basic"
4. Enable stats: true

Expected response: Obfuscated file attachment with statistics

## Step 6: Production Deployment

### 6.1 Using PM2 (Recommended)
```bash
# Install PM2 globally
npm install -g pm2

# Start bot with PM2
pm2 start bot.js --name "luau-obfuscator-bot"

# Save PM2 configuration
pm2 save

# Setup auto-restart on system reboot
pm2 startup
```

### 6.2 Using systemd (Linux)
Create `/etc/systemd/system/luau-bot.service`:
```ini
[Unit]
Description=Luau Obfuscator Discord Bot
After=network.target

[Service]
Type=simple
User=your-username
WorkingDirectory=/path/to/discord-bot
ExecStart=/usr/bin/node bot.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production

[Install]
WantedBy=multi-user.target
```

Enable and start:
```bash
sudo systemctl enable luau-bot
sudo systemctl start luau-bot
sudo systemctl status luau-bot
```

### 6.3 Using Docker
Create `Dockerfile`:
```dockerfile
FROM node:16-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .

USER node
CMD ["node", "bot.js"]
```

Build and run:
```bash
docker build -t luau-obfuscator-bot .
docker run -d --name luau-bot --env-file .env luau-obfuscator-bot
```

## Step 7: Monitoring and Maintenance

### 7.1 Log Monitoring
- Check console output for errors
- Monitor bot status in Discord
- Watch for rate limit warnings

### 7.2 Updates
```bash
# Stop bot
pm2 stop luau-obfuscator-bot

# Update dependencies
npm update

# Restart bot
pm2 start luau-obfuscator-bot
```

### 7.3 Backup
- Backup `.env` file (securely)
- Keep bot code in version control
- Document any custom configurations

## Troubleshooting

### Common Issues

#### Bot Not Responding
- ✅ Check bot token is correct
- ✅ Verify bot is online in Discord
- ✅ Check console for error messages
- ✅ Ensure bot has proper permissions

#### Commands Not Appearing
- ✅ Wait 5-10 minutes for command registration
- ✅ Check bot has `applications.commands` scope
- ✅ Restart bot if needed
- ✅ Verify CLIENT_ID is correct

#### File Upload Errors
- ✅ Check file size (max 1MB by default)
- ✅ Verify file extension is allowed
- ✅ Ensure file contains valid Lua code
- ✅ Check bot has "Attach Files" permission

#### Rate Limit Issues
- ✅ Adjust rate limit settings in `.env`
- ✅ Monitor usage patterns
- ✅ Consider implementing user-specific limits

### Debug Mode
Set environment variable for detailed logging:
```bash
NODE_ENV=development node bot.js
```

### Performance Optimization
- Monitor memory usage
- Implement caching if needed
- Consider horizontal scaling for high usage
- Use CDN for file attachments if necessary

## Security Considerations

### Token Security
- ✅ Never commit `.env` to version control
- ✅ Use environment variables in production
- ✅ Rotate tokens periodically
- ✅ Limit bot permissions to minimum required

### File Validation
- ✅ Validate file types and sizes
- ✅ Scan for malicious content if needed
- ✅ Implement rate limiting
- ✅ Log suspicious activity

### Server Security
- ✅ Keep Node.js updated
- ✅ Use HTTPS for all connections
- ✅ Implement proper firewall rules
- ✅ Regular security audits

## Support

### Getting Help
1. Check console logs for specific errors
2. Verify configuration matches examples
3. Test with simple Lua files first
4. Check Discord API status
5. Review bot permissions

### Useful Commands
```bash
# Check bot status
pm2 status

# View logs
pm2 logs luau-obfuscator-bot

# Restart bot
pm2 restart luau-obfuscator-bot

# Stop bot
pm2 stop luau-obfuscator-bot
```

## Conclusion

Your Luau Obfuscator Discord Bot should now be successfully deployed and ready to use! Users can upload Lua/Luau files and receive obfuscated versions through the simple `/obf` slash command interface.
