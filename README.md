# Luau Advanced Obfuscator

A high-quality, modular obfuscator for Luau (Roblox's Lua variant) that implements sophisticated obfuscation techniques for code protection.

## Features

- **Multi-layer String Encoding**: Nested character substitution with lookup tables
- **Dynamic String Construction**: Runtime string assembly using gsub operations
- **Code Structure Obfuscation**: Single-line compression and variable name randomization
- **Control Flow Obfuscation**: Complex nested function calls and indirect execution
- **Anti-Analysis Techniques**: Environment manipulation and dynamic code generation
- **Custom Character Ciphers**: Proprietary character mapping algorithms
- **Environment Manipulation**: Global environment hijacking and function redefinition

## Architecture

The obfuscator is organized into modular components:

```
src/
├── main.lua                    # Main entry point and orchestration
├── modules/
│   ├── string_obfuscator.lua   # String encoding and obfuscation
│   ├── structure_obfuscator.lua # Code structure transformation
│   ├── anti_analysis.lua       # Anti-analysis and evasion techniques
│   ├── cipher.lua              # Character encoding and cipher methods
│   ├── environment.lua         # Environment manipulation
│   └── utils.lua               # Utility functions and helpers
├── parser/
│   ├── lexer.lua               # Lua lexical analysis
│   └── ast.lua                 # Abstract syntax tree handling
└── output/
    └── generator.lua           # Obfuscated code generation

tests/
├── test_runner.lua             # Test execution framework
├── string_tests.lua            # String obfuscation tests
├── structure_tests.lua         # Structure obfuscation tests
└── integration_tests.lua       # End-to-end integration tests
```

## Usage

```lua
local Obfuscator = require("src.main")

-- Create obfuscator instance
local obfuscator = Obfuscator.new({
    stringObfuscation = true,
    structureObfuscation = true,
    antiAnalysis = true,
    environmentManipulation = true,
    compressionLevel = "high"
})

-- Obfuscate Lua code
local originalCode = [[
    local function greet(name)
        print("Hello, " .. name .. "!")
    end
    greet("World")
]]

local obfuscatedCode = obfuscator:obfuscate(originalCode)
print(obfuscatedCode)
```

## Compatibility

- Designed for Roblox's Luau runtime
- Compatible with Lua 5.1+ syntax
- Maintains functional equivalence of original code
- Optimized for Roblox environment constraints

## Security Notice

This tool is intended for legitimate code protection purposes. Users are responsible for ensuring compliance with applicable terms of service and legal requirements.
